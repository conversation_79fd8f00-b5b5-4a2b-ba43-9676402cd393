import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Swal from 'sweetalert2';
import { Editor } from 'primereact/editor';
import Header from '../Header';

// import '@uiw/react-md-editor/markdown-editor.css';
import { logoutUser, generateTranslation, addTranslation, getAllBrands } from '../../ServiceApi/Api';
import './Dashboard.css';

const Dashboard = () => {
   const navigate = useNavigate();
  // const [user, setUser] = useState(null);
  const [textToTranslate, setTextToTranslate] = useState('');
  const [translatedResults, setTranslatedResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const [translationTitle, setTranslationTitle] = useState('');
  const [selectedBrands, setSelectedBrands] = useState({
    'All Brands': false,
  });
  const [brands, setBrands] = useState([]);
  const [brandNames, setBrandNames] = useState([]);
  const [titleError, setTitleError] = useState('');
  const [textError, setTextError] = useState('');
  const [brandError, setBrandError] = useState('');


  useEffect(() => {
  const fetchBrands = async () => {
    try {
      const res = await getAllBrands();
      console.log('Raw brand data:', res.data);
      const brands = res.data

      const brandNames = res.data.map((brand) => `${brand.name} - ${brand.language}`);
      console.log({ brandNames });
      
      // Set brand names
      setBrandNames(brandNames);
      setBrands(brands);

      // Initialize selectedBrands with all set to false
      const initialSelectedBrands = {};
      brandNames.forEach(name => {
        initialSelectedBrands[name] = false;
      });

      setSelectedBrands(() => ({
        'All Brands': false,
        ...initialSelectedBrands
      }));
    } catch (err) {
      console.error('Error fetching brands:', err);
    }
  };

  fetchBrands();
}, []);
  

  const handleTitleChange = (e) => {
    setTranslationTitle(e.target.value);
    if (e.target.value.trim()) setTitleError('');
  };

  const handleTextChange = (e) => {
    setTextToTranslate(e.htmlValue || ''); // Store HTML, not just plain text
    if ((e.htmlValue?.trim() || '').length > 0) setTextError('');
  };

  const handleBrandChange = (brand) => {
    setSelectedBrands(prev => {
      const newState = { ...prev };
      if (brand === 'All Brands') {
        const checked = !prev['All Brands'];
        newState['All Brands'] = checked;
        brandNames.forEach(name => {
          newState[name] = checked;
        });
      } else {
        newState[brand] = !prev[brand];
      }
      // Update All Brands status based on individual selections
      const allSelected = brands.every(b => newState[b]);
      newState['All Brands'] = allSelected;

      // Remove error if at least one brand is selected
      const selectedLangs = mapBrandsToLanguages(newState);
      if (selectedLangs.length > 0) setBrandError('');
      return newState;
    });
  };

  const mapBrandsToLanguages = () => {
    const langs = [];
    console.log({ brands });
    if (selectedBrands['All Brands']) {
      return brands.map(b => b.id);
    }
    brandNames.forEach(name => {
      if (selectedBrands[name]) {
        const lang = brands.find(b => b.name === name.split(' - ')[0]).id;
        langs.push(lang);
      }
    });

    return langs;
  };

  const handleGenerate = async () => {
    setTitleError('');
    setTextError('');
    setBrandError('');
    const selectedLangs = mapBrandsToLanguages();
    // Use HTML as original_text
    const htmlText = textToTranslate;
    const plainText = textToTranslate.replace(/<[^>]*>/g, '').trim();
    let hasError = false;
    if (!translationTitle.trim()) {
      setTitleError('Please fill the mandatory field.');
      hasError = true;
    }
    if (!plainText) {
      setTextError('Please fill the mandatory field.');
      hasError = true;
    }
    if (selectedLangs.length === 0) {
      setBrandError('Please select at least one brand.');
      hasError = true;
    }
    if (hasError) return;
    setLoading(true);
    try {
      const res = await addTranslation({
        title: translationTitle,
        original_text: htmlText, // Store HTML
        brands: selectedLangs
      });
      // setTranslatedResults(res.data);
      Swal.fire({
        title: 'Success!',
        text: 'Translation added successfully',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
      });
      setTranslationTitle('');
      setTextToTranslate('');
      setSelectedBrands(prev => {
        const cleared = { ...prev };
        Object.keys(cleared).forEach(k => cleared[k] = false);
        return cleared;
      });
      setLoading(false);
      navigate('/translation-table');
    } catch (err) {
      console.error('Error fetching translations:', err);
      setLoading(false);
    }
    return;
  };


  return (
    <div className="dashboard-container">
      {/* <header className="dashboard-header">
        <div className="header-content">
          <h1>Translator Dashboard</h1>
          <div className="header-actions">
            <button className="update-translations-btn" onClick={handleBrands}>Dashboard</button>
            <button className="update-translations-btn" onClick={handleBrands}>Brands</button>
            <button className="update-translations-btn" onClick={handleUpdateTranslations}>Translations</button>
            <div className="user-dropdown-container">
              <button className="user-button" onClick={() => setShowUserDropdown(!showUserDropdown)}>
                <div className="user-avatar">V</div>
                <span>{user.name}</span>
              </button>
              {showUserDropdown && (
                <div className="user-dropdown">
                  <div className="dropdown-item">
                    <strong>{user.name}</strong>
                    <small>{user.email}</small>
                  </div>
                  <hr />
                  <button className="dropdown-item" onClick={handleLogout}>Logout</button>
                </div>
              )}
            </div>
          </div>
        </div>
      </header> */}
      <Header />

      <main className="dashboard-main">
        <div className="translation-container">
          <div className="translation-form">
            <div className="form-section">
              <label className="form-label">Title<span style={{ color: 'red' }}>*</span></label>
              <input
                type="text"
                value={translationTitle}
                onChange={handleTitleChange}
                placeholder="Enter a title"
                style={{
                  width: '100%',
                  padding: '8px',
                  fontSize: '14px',
                  borderRadius: '4px',
                  border: '1px solid #ccc',
                  marginBottom: '12px',
                }}
                required
              />
              {titleError && <div className="form-error" style={{ color: 'red', marginBottom: '8px' }}>{titleError}</div>}

              <label className="form-label">English Text to Translate<span style={{ color: 'red' }}>*</span></label>
              <Editor
                value={textToTranslate}
                onTextChange={handleTextChange}
                style={{ height: '200px', marginBottom: '10px' }}
                placeholder="Write or paste text here..."
              />
              {textError && <div className="form-error" style={{ color: 'red', marginBottom: '8px' }}>{textError}</div>}

              <div className="character-count">
                {textToTranslate.replace(/<[^>]*>/g, '').length} characters
              </div>
            </div>

            <div className="form-section">
              <label className="form-label">Select Brand(s)<span style={{ color: 'red' }}>*</span></label>
              <div style={{ marginTop: '8px' }}>
                <label style={{ display: 'inline-flex', alignItems: 'center', marginRight: '12px' }}>
                  <input
                    type="checkbox"
                    checked={selectedBrands['All Brands']}
                    onChange={() => handleBrandChange('All Brands')}
                    style={{ marginRight: '6px' }}
                  />
                  All Brands
                </label>
                <div style={{ marginTop: '8px', display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                  {brandNames.map((brand) => (
                    <label
                      key={brand}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        fontSize: '14px',
                        padding: '4px 8px',
                        border: '1px solid #ccc',
                        borderRadius: '4px',
                        backgroundColor: '#f9f9f9',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      <input
                        type="checkbox"
                        checked={selectedBrands[brand]}
                        onChange={() => handleBrandChange(brand)}
                        style={{ marginRight: '6px' }}
                      />
                      {brand}
                    </label>
                  ))}
                </div>
                {brandError && <div className="form-error" style={{ color: 'red', marginTop: '8px' }}>{brandError}</div>}
              </div>

            </div>

            <button
              className="generate-button"
              onClick={handleGenerate}
              disabled={loading}
            >
              {loading ? 'Translating...' : 'Generate'}
            </button>
          </div>

          {translatedResults.length > 0 && (
            <div
              className="results-section"
              style={{
                maxHeight: '300px',
                overflowY: 'auto',
                padding: '10px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                backgroundColor: '#fff',
                marginTop: '20px',
              }}
            >
              <h3 style={{ marginTop: 0 }}>Translated Text</h3>
              {translatedResults.map((res, index) => (
                <div
                  key={index}
                  style={{
                    padding: '6px 0',
                    borderBottom: '1px solid #eee',
                    fontSize: '14px',
                    display: 'flex',
                    gap: '6px',
                    alignItems: 'center',
                    flexWrap: 'wrap'
                  }}
                >
                  <strong>{res.detected_source_language} ➜</strong>
                  <span dangerouslySetInnerHTML={{ __html: res.text }} />
                </div>

              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
