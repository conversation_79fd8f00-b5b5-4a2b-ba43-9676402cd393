import React, { useRef, useState, useEffect } from 'react';
import 'primereact/resources/themes/saga-blue/theme.css';
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
import { Tooltip } from 'primereact/tooltip';
import './Fileupload.css';
import Header from '../Header';
import Swal from 'sweetalert2';
import { uploadFile, getImportResults } from '../../ServiceApi/Api';

const FileUploadComponent = () => {
    const FILE = import.meta.env.VITE_FILE_BASE_URL;
    const fileInputRef = useRef();
    const [activeIndex, setActiveIndex] = useState(0);
    const [selectedFileName, setSelectedFileName] = useState('');
    const [selectedFile, setSelectedFile] = useState(null);
    const [isUploading, setIsUploading] = useState(false);
    const [resultsData, setResultsData] = useState([]);
    const [interval, setInterval] = useState(false);

    const handleFileChange = (e) => {
        const file = e.target.files[0];
        if (!file) return;

        const fileName = file.name;
        const fileSize = file.size;
        const fileType = file.type;

        const allowedTypes = [
            'text/csv',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ];

        if (!allowedTypes.includes(fileType)) {
            Swal.fire({
                icon: 'error',
                title: 'Invalid File Type',
                text: 'Please upload only CSV or Excel files!',
                timer: 3000,
                showConfirmButton: false,
            });
            return;
        }

        if (fileSize > 5 * 1024 * 1024) {
            Swal.fire({
                icon: 'error',
                title: 'File Too Large',
                text: 'File size should not exceed 5MB!',
                timer: 3000,
                showConfirmButton: false,
            });
            return;
        }

        setSelectedFileName(fileName);
        setSelectedFile(file);
    };

    const handleUploadClick = async () => {
        if (!selectedFile) {
            Swal.fire({
                icon: 'warning',
                title: 'No File Selected',
                text: 'Please choose a file before uploading!',
            });
            return;
        }

        setIsUploading(true); // Disable the button while uploading

        try {
            const response = await uploadFile(selectedFile);
            console.log(response);

            if (response.success === true) {
                Swal.fire({
                    icon: 'success',
                    title: 'Upload Successful',
                    text: 'Your file has been uploaded successfully!',
                    timer: 3000,
                    showConfirmButton: false,
                });
                setSelectedFile(null);
                setSelectedFileName('');
                if (fileInputRef.current) fileInputRef.current.value = '';
                fetchResults();
            } else {
                throw new Error('Upload failed');
            }
        } catch (error) {
            console.error('Upload error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Upload Failed',
                text: 'There was an error uploading your file.',
                timer: 3000,
                showConfirmButton: false,
            });
        } finally {
            setIsUploading(false); // Re-enable the button
        }
    };

    useEffect(() => {
        fetchResults(); // Initial fetch

        return () => {
            // Cleanup when component unmounts
        };
    }, []);
    const fetchResults = async () => {
        try {
            const response = await getImportResults();
            console.log(response, "File Results");
            setResultsData(response.file);

            // Check if any items are still pending
            const hasPending = response.file.some(item => item.status === 'pending');
            if (hasPending) {
                // If pending items exist, schedule next poll in 5 seconds
                setTimeout(fetchResults, 5000);
            }
        } catch (error) {
            console.error('Error fetching results:', error);
            // Retry after error with exponential backoff
            setTimeout(fetchResults, 10000);
        }
    };



    return (
        <div className="page-container">
            <Header />
            <main className="page-main" style={{ paddingTop: '20px' }}>
                <div className="custom-tabs p-mb-4" style={{ marginTop: '20px' }}>
                    {/* Upload Section */}
                    <div className="tab-content">
                        <div className="file-upload-container" style={{ backgroundColor: '#fff', borderRadius: '8px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)', padding: '24px' }}>
                            <div style={{ marginBottom: '24px' }}>
                                <h2 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '8px', color: '#2c3e50' }}>Upload & Process File</h2>
                                <p style={{ fontSize: '14px', color: '#7f8c8d' }}>Supported formats: CSV, Excel (Max 5MB)</p>
                            </div>

                            <input
                                type="file"
                                ref={fileInputRef}
                                style={{ display: 'none' }}
                                accept=".csv,.xlsx"
                                onChange={handleFileChange}
                            />

                            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                                <button
                                    onClick={() => fileInputRef.current?.click()}
                                    style={{
                                        minWidth: '150px',
                                        padding: '10px 16px',
                                        borderRadius: '4px',
                                        border: '1px solid #3498db',
                                        backgroundColor: 'transparent',
                                        color: '#3498db',
                                        cursor: 'pointer',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        transition: 'all 0.2s ease'
                                    }}
                                    onMouseEnter={(e) => e.target.style.backgroundColor = '#f0f7fd'}
                                    onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
                                >
                                    <i className="pi pi-folder-open" style={{ marginRight: '8px' }}></i>
                                    Choose File
                                </button>
                                <div style={{
                                    flexGrow: 1,
                                    padding: '12px',
                                    border: '1px dashed #bdc3c7',
                                    borderRadius: '4px',
                                    backgroundColor: '#f8f9fa',
                                    minHeight: '40px',
                                    display: 'flex',
                                    alignItems: 'center'
                                }}>
                                    {selectedFileName || <span style={{ color: '#95a5a6' }}>No file chosen</span>}
                                </div>
                                <button
                                    onClick={handleUploadClick}
                                    disabled={!selectedFile || isUploading}
                                    style={{
                                        minWidth: '150px',
                                        padding: '10px 16px',
                                        borderRadius: '4px',
                                        border: 'none',
                                        backgroundColor: !selectedFile || isUploading ? '#bdc3c7' : '#3498db',
                                        color: '#fff',
                                        cursor: !selectedFile || isUploading ? 'not-allowed' : 'pointer',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        transition: 'all 0.2s ease'
                                    }}
                                    onMouseEnter={(e) => {
                                        if (!(!selectedFile || isUploading)) {
                                            e.target.style.backgroundColor = '#2980b9';
                                        }
                                    }}
                                    onMouseLeave={(e) => {
                                        if (!(!selectedFile || isUploading)) {
                                            e.target.style.backgroundColor = '#3498db';
                                        }
                                    }}
                                >
                                    <i className="pi pi-upload" style={{ marginRight: '8px' }}></i>
                                    {isUploading ? 'Uploading...' : 'Upload'}
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Results Section */}
                    <div className="tab-content">
                        <div className="table-container">
                            <div className="table-scroll p-datatable-wrapper" style={{ maxHeight: '500px', overflowY: 'auto' }}>
                                <table className="p-datatable-table p-datatable-striped" style={{ width: '100%' }}>
                                    <thead className="p-datatable-thead" style={{ position: 'sticky', top: 0, backgroundColor: 'white', zIndex: 1 }}>
                                        <tr>
                                            <th style={{ minWidth: '50px' }}>S. No</th> {/* ✅ Added S. No */}
                                            {/* <th style={{ minWidth: '50px' }}>ID</th> */}
                                            <th style={{ minWidth: '150px' }}>File Name</th>
                                            <th style={{ minWidth: '100px' }}>Status</th>
                                            <th style={{ minWidth: '80px' }}>Result</th>
                                            <th style={{ minWidth: '120px' }}>Created By</th>
                                            <th style={{ minWidth: '200px' }}>Message</th>
                                        </tr>
                                    </thead>
                                    <tbody className="p-datatable-tbody">
                                        {resultsData.map((item,index) => (
                                            <tr key={item.id}>

                                                <td>{index + 1}</td> {/* ✅ S. No displayed */}
                                                <td>{item.file_name}</td>
                                                {/* <td
                                                    style={{
                                                        color:
                                                            item.status === 'completed'
                                                                ? 'green'
                                                                : item.status === 'pending'
                                                                    ? 'orange'
                                                                    : 'red',
                                                    }}
                                                >
                                                    {item.status}
                                                </td> */}
                                                <td>
                                                    <span
                                                        style={{
                                                            backgroundColor:
                                                                item.status === 'completed'
                                                                    ? 'green'
                                                                    : item.status === 'pending'
                                                                        ? 'orange'
                                                                        : 'red',
                                                            color: 'white',
                                                            padding: '4px 10px',
                                                            borderRadius: '12px',
                                                            fontSize: '0.75rem',
                                                            fontStfontWeightyle: 'bold',
                                                            textTransform: 'capitalize',
                                                            display: 'inline-block',
                                                            minWidth: '80px',
                                                            textAlign: 'center',
                                                        }}
                                                    >
                                                        {item.status}
                                                    </span>
                                                </td>

                                                <td>
                                                    {item.result && item.status === 'completed' ? (
                                                        <a
                                                            href={`${FILE}/${item.result_path}`}
                                                            download
                                                            className="p-button p-button-rounded p-button-text"
                                                        >
                                                            <i className="pi pi-download"></i>
                                                        </a>
                                                    ) : (
                                                        item.result || '-'
                                                    )}
                                                </td>
                                                <td>{item.created_by?.name || '-'}</td>
                                                <td>
                                                    {item.error_message ? (
                                                        <>
                                                            <i
                                                                className="pi pi-info-circle p-mr-2"
                                                                style={{ color: 'red', cursor: 'pointer' }}
                                                                data-pr-tooltip={item.error_message}

                                                            />
                                                            <Tooltip target=".pi-info-circle" position='left' />
                                                        </>
                                                    ) : '-'}
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    );
};

export default FileUploadComponent;