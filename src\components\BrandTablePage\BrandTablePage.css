.brand-table-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 16px 0px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1600px;
  margin: 0 auto;
  width: 95%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  background: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.back-button:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.page-header h1 {
  margin: 0;
  color: #333;
  font-size: 28px;
  font-weight: 700;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-dropdown-container {
  position: relative;
}

.user-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #333;
}

.user-button:hover {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: #4285f4;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.dropdown-arrow {
  width: 16px;
  height: 16px;
  color: #666;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 120px;
  z-index: 1000;
  overflow: hidden;
}

.dropdown-item {
  padding: 12px 16px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item strong {
  display: block;
  color: #333;
}

.dropdown-item small {
  color: #666;
  font-size: 12px;
}

.page-main {
  flex: 1;
  padding: 24px;
  display: flex;
  justify-content: center;
}

.table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 1600px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 120px);
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 80px 20px;
  color: #666;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #4285f4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.table-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.table-header {
  padding: 24px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.table-header-left {
  flex: 1;
}

.table-header-right {
  display: flex;
  align-items: center;
}

.brand-filter {
  display: flex;
  align-items: center;
  gap: 8px;
}

.brand-filter label {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.brand-dropdown {
  padding: 8px 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.brand-dropdown:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.brand-dropdown:hover {
  border-color: #667eea;
}

/* Bulk Delete Button */
.bulk-delete-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.bulk-delete-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.bulk-delete-btn svg {
  width: 16px;
  height: 16px;
}

.bulk-delete-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.bulk-delete-btn:disabled:hover {
  background: #6c757d;
  transform: none;
  box-shadow: none;
}

/* Checkbox Styles */
.checkbox-header, .checkbox-cell {
  width: 40px;
  text-align: center;
  padding: 8px !important;
}

.checkbox-header input[type="checkbox"],
.checkbox-cell input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
  accent-color: #667eea;
}

/* Action Column Styles */
.action-cell {
  padding: 12px 8px !important;
  text-align: center;
  white-space: nowrap;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

.edit-btn, .delete-btn {
  padding: 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-btn svg, .delete-btn svg {
  width: 16px;
  height: 16px;
}

.edit-btn {
  background: #28a745;
  color: white;
}

.edit-btn:hover {
  background: #218838;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.delete-btn {
  background: #dc3545;
  color: white;
}

.delete-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* Edit Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.modal-header h3 {
  margin: 0;
  color: #495057;
  font-size: 18px;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #6c757d;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #e9ecef;
  color: #495057;
}

.modal-body {
  padding: 24px;
  max-height: 400px;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.form-textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  transition: border-color 0.2s ease;
}

.form-textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Rich Text Editor Styles */
.rich-text-editor {
  border: 2px solid #e9ecef;
  border-radius: 6px;
  overflow: hidden;
  transition: border-color 0.2s ease;
}

.rich-text-editor:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.editor-toolbar {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 8px 12px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.toolbar-btn {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 32px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.toolbar-btn:active {
  background: #dee2e6;
}

.rich-text-content {
  padding: 12px;
  min-height: 120px;
  max-height: 200px;
  overflow-y: auto;
  font-size: 14px;
  line-height: 1.5;
  color: #495057;
  background: white;
  outline: none;
}

.rich-text-content:empty:before {
  content: attr(data-placeholder);
  color: #6c757d;
  font-style: italic;
}

.rich-text-content p {
  margin: 0 0 8px 0;
}

.rich-text-content p:last-child {
  margin-bottom: 0;
}

.rich-text-content ul {
  margin: 8px 0;
  padding-left: 20px;
}

.rich-text-content li {
  margin-bottom: 4px;
}

.modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: #f8f9fa;
}

.cancel-btn, .save-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #5a6268;
}

.save-btn {
  background: #28a745;
  color: white;
}

.save-btn:hover:not(:disabled) {
  background: #218838;
}

.save-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

/* Rich Text Editor Styles */
.rich-text-editor {
  border: 2px solid #e9ecef;
  border-radius: 6px;
  overflow: hidden;
  transition: border-color 0.2s ease;
}

.rich-text-editor:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.editor-toolbar {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 8px 12px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.toolbar-btn {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 32px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.toolbar-btn:active {
  background: #dee2e6;
}

.rich-text-content {
  padding: 12px;
  min-height: 120px;
  max-height: 200px;
  overflow-y: auto;
  font-size: 14px;
  line-height: 1.5;
  color: #495057;
  background: white;
  outline: none;
}

.rich-text-content:empty:before {
  content: "Enter original text...";
  color: #6c757d;
  font-style: italic;
}

.rich-text-content p {
  margin: 0 0 8px 0;
}

.rich-text-content p:last-child {
  margin-bottom: 0;
}

.rich-text-content ul {
  margin: 8px 0;
  padding-left: 20px;
}

.rich-text-content li {
  margin-bottom: 4px;
}

.table-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.table-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.table-scroll {
  flex: 1;
  overflow: auto;
}

.brands-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.brands-table th {
  background: #f8f9fa;
  color: #333;
  font-weight: 600;
  padding: 16px;
  text-align: left;
  border-bottom: 2px solid #e9ecef;
  position: sticky;
  top: 0;
  z-index: 10;
}

.brands-table td {
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
  vertical-align: top;
}

.brands-table tr:hover {
  background: #f8f9fa;
}

.text-cell {
  max-width: 350px;
}

.text-content {
  max-height: 100px;
  overflow: auto;
  line-height: 1.4;
  word-wrap: break-word;
}

.language-badge {
  background: #4285f4;
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.brand-badge {
  background: #28a745;
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background: #f8d7da;
  color: #721c24;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

.no-data-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 80px 20px;
  text-align: center;
}

.no-data-container h2 {
  color: #333;
  margin-bottom: 16px;
}

.no-data-container p {
  color: #666;
  margin-bottom: 24px;
}

/* Responsive design */
@media (max-width: 768px) {
  .page-header {
    padding: 12px 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .page-header h1 {
    font-size: 24px;
  }
  
  .page-main {
    padding: 16px;
  }
  
  .table-container {
    max-height: calc(100vh - 160px);
  }
  
  .table-header {
    padding: 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .table-header-right {
    width: 100%;
    justify-content: flex-start;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .edit-btn, .delete-btn {
    font-size: 14px;
    padding: 6px;
    width: 28px;
    height: 28px;
  }

  .bulk-delete-btn {
    font-size: 12px;
    padding: 6px 12px;
    margin-right: 8px;
  }

  .checkbox-header, .checkbox-cell {
    width: 30px;
    padding: 4px !important;
  }

  .modal-content {
    width: 95%;
    margin: 20px;
  }

  .modal-header, .modal-body, .modal-footer {
    padding: 16px;
  }

  .modal-footer {
    flex-direction: column;
    gap: 8px;
  }

  .cancel-btn, .save-btn {
    width: 100%;
  }
  
  .brands-table {
    font-size: 12px;
  }
  
  .brands-table th,
  .brands-table td {
    padding: 12px 8px;
  }
  
  .text-cell {
    max-width: 150px;
  }
  
  .text-content {
    max-height: 60px;
  }
}
