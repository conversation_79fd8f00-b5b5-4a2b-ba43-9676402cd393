.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.dashboard-header {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 16px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-dropdown-container {
  position: relative;
}

.user-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.user-button:hover {
  background-color: #f8f9fa;
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: #4285f4;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.dropdown-arrow {
  width: 16px;
  height: 16px;
  color: #666;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  z-index: 1000;
  margin-top: 4px;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 12px 16px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item strong {
  display: block;
  color: #333;
}

.dropdown-item small {
  color: #666;
  font-size: 12px;
}

.update-translations-btn {
  /* background: #28a745; */
  /* color: white; */
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: 16px;
}

.update-translations-btn:hover {
  /* background: #218838; */
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.dropdown-item hr {
  margin: 8px 0;
  border: none;
  border-top: 1px solid #e9ecef;
}

.update-translations-btn {
  background: none;
  /* color: white; */
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.update-translations-btn:hover {
  /* background: #3367d6; */
  /* color: white; */
  text-decoration: underline;
}

.dashboard-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px 24px;
}

.translation-container {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.translation-form {
  max-width: 100%;
}

.form-section {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

.translation-textarea {
  width: 100%;
  min-height: 200px;
  padding: 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  transition: border-color 0.2s;
}

.translation-textarea:focus {
  outline: none;
  border-color: #4285f4;
}

.translation-textarea::placeholder {
  color: #999;
}

.character-count {
  text-align: right;
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #4285f4;
}

.brands-container {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.brand-checkbox {
  padding: 8px 0;
}

.brand-checkbox input:disabled {
  opacity: 0.5;
}

.generate-button {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 8px;
}

.generate-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.generate-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 18px;
  color: #667eea;
}

.results-section {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e9ecef;
}

.results-section h3 {
  color: #333;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 24px;
  text-align: center;
}

.language-section {
  margin-bottom: 32px;
}

.language-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #333;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border-left: 4px solid #4285f4;
}

.language-flag {
  font-size: 20px;
}

.language-results {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.result-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.result-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.result-card.success {
  border-left: 4px solid #28a745;
}

.result-card.error {
  border-left: 4px solid #dc3545;
  background: #fff8f8;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.brand-name {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.result-card.success .status-badge {
  background: #d4edda;
  color: #155724;
}

.result-card.error .status-badge {
  background: #f8d7da;
  color: #721c24;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.text-row {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.text-label {
  font-size: 12px;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.text-value {
  font-size: 14px;
  line-height: 1.4;
  color: #333;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.text-value.translated {
  background: #e8f5e8;
  border-color: #28a745;
  font-weight: 500;
}

.text-row.error .text-value {
  background: #ffeaea;
  border-color: #dc3545;
  color: #dc3545;
}

/* Responsive design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .dashboard-main {
    padding: 20px 16px;
  }

  .translation-container {
    padding: 20px;
  }

  .user-dropdown {
    right: auto;
    left: 0;
  }

  .language-title {
    font-size: 16px;
    padding: 10px 12px;
  }

  .language-flag {
    font-size: 18px;
  }

  .result-card {
    padding: 12px;
  }

  .text-row {
    gap: 2px;
  }

  .text-value {
    padding: 6px 8px;
    font-size: 13px;
  }

  .brand-name {
    font-size: 13px;
  }

  .status-badge {
    font-size: 11px;
    padding: 3px 6px;
  }
}
