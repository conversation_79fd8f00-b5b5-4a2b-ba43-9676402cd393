import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Login from './components/Login';
import Dashboard from './components/Dashboard';
import BrandTablePage from './components/BrandTablePage/BrandTablePage';
import TranslationTablePage from './components/TranslationTablePage/TranslationTablePage';
import ProtectedRoute from './components/ProtectedRoute';
import './App.css';
import UserTablePage from './components/UsersTable/UserTablePage';
import FileUploadPage from './components/FileUpload/Fileupload';
import GlossaryTablePage from './components/Glossary/GlossaryTable';


function App() {
  // Check if user is already logged in
  const isAuthenticated = () => {
    const userData = localStorage.getItem('user');
    if (!userData) return false;

    try {
      const parsedUser = JSON.parse(userData);
      return parsedUser.token ? true : false;
    } catch (error) {
      return false;
    }
  };

  return (
    <Router>
      <div className="App">
        <Routes>
          <Route
            path="/login"
            element={
              isAuthenticated() ? <Navigate to="/dashboard" replace /> : <Login />
            }
          />
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            }
          />
          <Route
            path="/brands"
            element={
              <ProtectedRoute>
                <BrandTablePage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/translation-table"
            element={
              <ProtectedRoute>
                <TranslationTablePage />
              </ProtectedRoute>
            }
          />
             <Route
            path="/glossary"
            element={
              <ProtectedRoute>
                <GlossaryTablePage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/user"
            element={
              <ProtectedRoute>
                <UserTablePage />
              </ProtectedRoute>
            }
          />
               <Route
            path="/uploads"
            element={
              <ProtectedRoute>
                <FileUploadPage/>
              </ProtectedRoute>
            }
          />
          <Route
            path="/"
            element={
              <Navigate to={isAuthenticated() ? "/dashboard" : "/login"} replace />
            }
          />
          <Route
            path="*"
            element={
              <Navigate to={isAuthenticated() ? "/dashboard" : "/login"} replace />
            }
          />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
