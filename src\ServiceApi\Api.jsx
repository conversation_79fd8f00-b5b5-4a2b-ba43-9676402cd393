// Delete User(s)

import axios from 'axios';

// Environment Variables
const BASE_URL = import.meta.env.VITE_BASE_URL;
const getToken = () => {
  const user = localStorage.getItem("user");
  return user ? JSON.parse(user).token : null;
};



// const DEEPL_API_URL = import.meta.env.VITE_DEEPL_API_URL;
// const DEEPL_AUTH_KEY = import.meta.env.VITE_DEEPL_API_KEY;
// const DEEPL_GLOSSARY_ID = import.meta.env.VITE_DEEPL_GLOSSARY_ID;



// ==================== AUTHENTICATION APIs ====================

// Update User

// Login API
export const loginUser = async (email, password) => {
  try {
    const response = await axios.post(`${BASE_URL}/auth/login`, {
      email,
      password
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log("Login API Response:", response.data);

    if (response.data.success === true) {
      localStorage.setItem("user", JSON.stringify(response.data.data));
      return response.data;

    } else {
      throw new Error(response.data.message || 'Login failed');
    }
  } catch (error) {
    console.error("Login request failed:", error.response?.data || error.message);
    throw error;
  }
};

// Logout function to clear localStorage
export const logoutUser = () => {
  localStorage.removeItem("user");
  localStorage.clear(); // Clear all localStorage data
};

export const generateTranslation = async (data) => {
  try {
    const url = `${BASE_URL}/translate`;
    console.log(url, "++++++");

    const response = await axios.post(url, data, {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json',
      },
    });

    console.log("Get All Translations API Response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Get all translations request failed:", error.response?.data || error.message);
    throw error;
  }
};

export const addTranslation = async (data) => {
  try {
    const url = `${BASE_URL}/translation`;
    console.log(url, "++++++");
    const response = await axios.post(url, data, {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json',
      },
    });

    console.log("Get All Translations API Response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Get all translations request failed:", error.response?.data || error.message);
    throw error;
  }
};
export const copyTranslation = async (data) => {
  try {
    const url = `${BASE_URL}/translation/copy`;
    console.log(url, "++++++");
    const response = await axios.post(url, data, {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json',
      },
    });

    console.log("Get All Translations API Response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Get all translations request failed:", error.response?.data || error.message);
    throw error;
  }
};

export const getAllTranslations = async (brand) => {
  console.log(brand, "uibfiuwegfyuewvfyvfyvwfy")
  try {
    const url = `${BASE_URL}/translation?brand=${brand}`;
    console.log(url);
    const response = await axios.get(url, {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      }
    });

    console.log("Get All Translations API Response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Get all translations request failed:", error.response?.data || error.message);
    throw error;
  }
};

export const resyncTranslation = async (brand) => {
  console.log(brand, "uibfiuwegfyuewvfyvfyvwfy")
  try {
    const url = `${BASE_URL}/translation/resync?brand=${brand}`;
    console.log(url);
    const response = await axios.get(url, {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      }
    });

    console.log("Get Resync API Response:", response);
    return response;
  } catch (error) {
    console.error("Get Resync request failed:", error.response?.data || error.message);
    throw error;
  }
};

// Update Translation
export const updateTranslation = async (translationData, id) => {
  try {
    console.log("Update Translation API Request URL:", `${BASE_URL}/translation/${id}`);
    console.log("Update Translation API Request Payload:", JSON.stringify(translationData, null, 2));
    console.log("Update Translation API Request Headers:", {
      'Content-Type': 'application/json',
    });

    const response = await axios.put(`${BASE_URL}/translation/${id}`, translationData, {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json',
      }
    });

    console.log("Update Translation API Response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Update translation request failed:");
    console.error("Error status:", error.response?.status);
    console.error("Error data:", error.response?.data);
    console.error("Error message:", error.message);
    console.error("Full error:", error);
    throw error;
  }
};

// Delete Translation(s)
export const deleteTranslations = async (ids) => {
  try {
    const response = await axios.delete(`${BASE_URL}/translation`, {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      },
      data: {
        ids: Array.isArray(ids) ? ids : [ids]
      }
    });

    console.log("Delete Translation API Response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Delete translation request failed:", error.response?.data || error.message);
    throw error;
  }
};


export const addBrand = async (data) => {
  try {
    const url = `${BASE_URL}/brand`;
    const response = await axios.post(url, data, {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      },
    });

    console.log("Get All Brands API Response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Get all translations request failed:", error.response?.data || error.message);
    throw error;
  }
};

export const getAllBrands = async () => {
  try {
    const url = `${BASE_URL}/brand`;
    const response = await axios.get(url, {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      }
    });

    console.log("Get All Brands API Response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Get all Brands request failed:", error.response?.data || error.message);
    throw error;
  }
};

// Update Translation
export const updateBrand = async (Data, id) => {
  try {
    console.log("Update Brand API Request URL:", `${BASE_URL}/brand/${id}`);
    console.log("Update Brand API Request Payload:", JSON.stringify(Data, null, 2));
    console.log("Update Brand API Request Headers:", {
      'Content-Type': 'application/json',
    });

    const response = await axios.put(`${BASE_URL}/brand/${id}`, Data, {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json',
      }
    });

    console.log("Update Brand API Response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Update Brand request failed:");
    console.error("Error status:", error.response?.status);
    console.error("Error data:", error.response?.data);
    console.error("Error message:", error.message);
    console.error("Full error:", error);
    throw error;
  }
};

// Delete Brand(s)
export const deleteBrands = async (ids) => {
  try {
    const response = await axios.delete(`${BASE_URL}/brand`, {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      },
      data: {
        ids: Array.isArray(ids) ? ids : [ids]
      }
    });

    console.log("Delete Brand API Response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Delete Brand request failed:", error.response?.data || error.message);
    throw error;
  }
};

export const getAllUsers = async () => {
  try {
    const url = `${BASE_URL}/users`;
    const response = await axios.get(url, {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      }
    });

    console.log("Get All Users API Response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Get all translations request failed:", error.response?.data || error.message);
    throw error;
  }
};

export const addUser = async ({ name, email, password }) => {
  try {
    const url = `${BASE_URL}/users`;
    const response = await axios.post(url, { name, email, password }, {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      }
    });
    console.log('Add User API Response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Add user request failed:', error.response?.data || error.message);
    throw error;
  }
};
export const updateUser = async (data, id) => {
  try {
    const url = `${BASE_URL}/users/${id}`;
    console.log("Update User API Request URL:", url);
    console.log("Update User API Request Payload:", JSON.stringify(data, null, 2));
    const response = await axios.put(url, data, {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      }
    });
    console.log("Update User API Response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Update User request failed:", error.response?.data || error.message);
    throw error;
  }
};
export const deleteUsers = async (ids) => {
  try {
    const response = await axios.delete(`${BASE_URL}/users`, {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      },
      data: {
        ids: Array.isArray(ids) ? ids : [ids]
      }
    });
    console.log("Delete User API Response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Delete User request failed:", error.response?.data || error.message);
    throw error;
  }
};
// File Upload API
export const uploadFile = async (file) => {
  const formData = new FormData();
  formData.append('file', file);

  try {
    const response = await axios.post(`${BASE_URL}/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'Authorization': `Bearer ${getToken()}`,
        'ngrok-skip-browser-warning': 'true'
      }
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getImportResults = async () => {
  try {
    const response = await axios.get(`${BASE_URL}/import`, {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      }
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Get Glossary Items
export const getGlossary = async () => {
  try {
    const response = await axios.get(`${BASE_URL}/glossary`, {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      }
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Delete Glossary Item(s)
export const deleteGlossary = async (ids) => {
  try {
    const response = await axios.delete(`${BASE_URL}/glossary`, {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      },
      data: {
        ids: Array.isArray(ids) ? ids : [ids]
      }
    });

    console.log("Delete Glossary API Response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Delete glossary request failed:", error.response?.data || error.message);
    throw error;
  }
};

export const updateGlossary = async (id, data) => {
    try {
        const response = await axios.put(`${BASE_URL}/glossary/${id}`, data, {
            headers: {
                'Authorization': `Bearer ${getToken()}`,
                'Content-Type': 'application/json',
            }
        });

        console.log("Update Glossary API Response:", response.data);
        return response.data;
    } catch (error) {
        console.error("Update glossary request failed:", error.response?.data || error.message);
        throw error;
    }
};

export const createGlossary = async ({ entries }) => {
  try {
    const response = await axios.post(`${BASE_URL}/glossary`, {entries} , {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  } catch (error) {
    console.error('Failed to create glossary:', error);
    throw error;
  }
};