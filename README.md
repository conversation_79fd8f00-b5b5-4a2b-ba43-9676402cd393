# DeepL Translator Dashboard

A React application with authentication, API integration, and a clean dashboard interface.

## Features

- **Authentication System**: Login with email/password
- **Protected Routes**: Dashboard accessible only after login
- **Translation Interface**: Text translation with brand selection
- **API Integration**: Centralized API service structure
- **Token Management**: JWT token storage and management
- **Sweet Alerts**: Beautiful success/error notifications
- **Responsive Design**: Clean and modern UI
- **Auto-redirect**: Prevents access to login when already authenticated
- **User Dropdown**: Profile management and logout functionality
- **Brand Management**: Select specific brands or apply to all

## Project Structure

```
src/
├── components/
│   ├── Login/
│   │   ├── Login.jsx      # Login component
│   │   ├── Login.css      # Login styles
│   │   └── index.js       # Login export
│   ├── Dashboard/
│   │   ├── Dashboard.jsx  # Dashboard component
│   │   ├── Dashboard.css  # Dashboard styles
│   │   └── index.js       # Dashboard export
│   └── ProtectedRoute.jsx # Route protection component
├── ServiceApi/
│   ├── Api.jsx            # All API functions (Login & Translation only)
│   └── index.js           # API exports
├── App.jsx                # Main app with routing
├── App.css                # App styles
├── index.css              # Global styles
└── main.jsx               # App entry point
```

## API Structure

### Environment Variables
```env
VITE_BASE_URL=https://c8e6-106-51-152-127.ngrok-free.app
VITE_DEEPL_API_KEY=********-5014-407f-ac7e-************
VITE_DEEPL_GLOSSARY_ID=0a52855b-5768-48e0-8800-5ed1fc91ee30
```

### API Functions

#### Login API
```javascript
import { loginUser } from './ServiceApi/Api';

const response = await loginUser(email, password);
```

#### Translation API (DeepL Integration)
```javascript
import { generateTranslation, translateWithDeepL, getAllTranslations } from './ServiceApi/Api';

// Generate translation for multiple brands
const translationData = {
  text: "Hello World",
  selectedBrands: ["Mistycasino - Turkish", "Goldpot - Indonesian"]
};
const response = await generateTranslation(translationData);

// Direct DeepL translation
const result = await translateWithDeepL("Hello World", "TR", "EN");

// Get all translations
const allTranslations = await getAllTranslations('mistycasino');

// Response format:
{
  success: true,
  results: [
    {
      brand: "Mistycasino - Turkish",
      targetLanguage: "TR",
      originalText: "Hello World",
      translatedText: "Merhaba Dünya",
      success: true
    }
  ],
  totalTranslations: 1,
  successfulTranslations: 1
}
```

#### Deals API (Example)
```javascript
import { deals } from './ServiceApi/Api';

const dealsData = await deals();
```

#### Logout
```javascript
import { logoutUser } from './ServiceApi/Api';

logoutUser(); // Clears localStorage
```

## Authentication Flow

1. **Login**: User enters credentials
2. **API Call**: Sends POST request to `/api/auth/login`
3. **Success Response**: Stores user data and token in localStorage
4. **Sweet Alert**: Shows "Login Successfully" message
5. **Redirect**: Navigates to dashboard
6. **Protected Access**: Dashboard checks for valid token
7. **Logout**: Clears localStorage and redirects to login

## Installation & Setup

1. **Install dependencies**
```bash
npm install
```

2. **Set up environment variables**
Create a `.env` file in the root directory:
```env
VITE_BASE_URL=your-api-base-url
```

3. **Run the development server**
```bash
npm run dev
```

4. **Open in browser**
Navigate to `http://localhost:5173`

## Usage

### Login Credentials
Use the following test credentials:
- **Email**: <EMAIL>
- **Password**: Vikram@2000

### Translation Dashboard Features

#### Brand Selection
- **Apply to all Brands**: When checked, shows brand selection options and automatically selects all brands
- **Individual Selection**: When unchecked, hides brand selection section
- **Smart Visibility**: Brand selection only appears when "Apply to all Brands" is checked
- **Available Brands**:
  - Mistycasino - Turkish (translates to Turkish - TR)
  - Goldpot - Indonesian (translates to Indonesian - ID)

#### Translation Process
1. Enter text in the "English Text to translate" field
2. Choose brand selection method:
   - Check "Apply to all Brands" to show and select all brands
   - Uncheck to hide brand selection (no brands selected)
3. Click "Generate" to start translation
4. View results organized by language:
   - 🇹🇷 Turkish (TR) translations section
   - 🇮🇩 Indonesian (ID) translations section
5. Click "Update Translation" to view all existing translations in a table format

#### API Integration
- Uses DeepL API for high-quality translations
- Supports multiple target languages based on brand selection
- Displays success/error status for each translation
- Shows character count for input text

### Adding New APIs

1. Add API functions to `src/ServiceApi/Api.jsx`
2. Functions are automatically exported through `src/ServiceApi/index.js`
3. Import and use in components:

```javascript
import { yourApiFunction } from '../ServiceApi/Api';

const handleApiCall = async () => {
  try {
    const response = await yourApiFunction();
    // Handle success
  } catch (error) {
    // Handle error
  }
};
```

## Technologies Used

- **React 19**: Frontend framework
- **Vite**: Build tool and dev server
- **React Router DOM**: Client-side routing
- **Axios**: HTTP client for API calls
- **SweetAlert2**: Beautiful alert dialogs
- **CSS3**: Modern styling with gradients and animations

## Key Features Implemented

✅ Login API integration with base URL from environment
✅ Token storage in localStorage
✅ Protected routes with authentication check
✅ Sweet Alert notifications
✅ Auto-redirect based on authentication status
✅ Clean and responsive UI design
✅ Centralized API service structure
✅ Logout functionality with localStorage cleanup
✅ Translation interface with text input
✅ Brand selection (individual or all brands)
✅ User dropdown with profile info
✅ Organized folder structure (Login/Dashboard in separate folders)
✅ Translation API integration
✅ Character count for text input
✅ Professional dashboard design matching the provided mockup
✅ Smart brand selection visibility (shows only when "Apply to all Brands" is checked)
✅ Separate language sections for TR and ID results
✅ Flag icons and visual language separation
✅ Enhanced result cards with success/error indicators
✅ "Update Translation" button to view all existing translations
✅ Modal table component for displaying translation history
✅ Responsive table design with search and filter capabilities
