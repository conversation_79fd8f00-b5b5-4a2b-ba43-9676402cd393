import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Swal from 'sweetalert2';
import { Editor } from 'primereact/editor';
import '@uiw/react-md-editor/markdown-editor.css';
import { getAllBrands, addBrand, logoutUser, updateBrand, deleteBrands } from '../../ServiceApi/Api';
import './BrandTablePage.css';
import Header from '../Header';

const BrandTablePage = () => {
  const [allBrands, setAllBrands] = useState([]); // Original data
  const [filteredBrands, setFilteredBrands] = useState([]); // Displayed data
  const [loading, setLoading] = useState(true);
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const [selectedItems, setSelectedItems] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingBrand, setEditingBrand] = useState(null);
  const [createForm, setCreateForm] = useState({
    name: '',
    url: '',
    language: '',
    langcode: ''

  });
  const [searchTitle, setSearchTitle] = useState('');
  const [editForm, setEditForm] = useState({
    id: '',
    name: '',
    url: '',
    language: '',
    langcode: ''
  });
  const navigate = useNavigate();

  // Create brand
  const handleCreateBrand = async () => {
    try {
      const { name, url, language, langcode } = createForm;
      if (!name) {
        Swal.fire({
          icon: 'error',
          title: 'Oops...',
          text: 'Name is required!',
        });
        return;
      }
      const response = await addBrand({ name, url, language, langcode });
      console.log({ response });
      if (response.success === true) {
        Swal.fire({
          icon: 'success',
          title: 'Brand created successfully!',
          showConfirmButton: false,
          timer: 1500
        });
        setShowCreateModal(false);
        setCreateForm({
          name: '',
          url: '',
          language: '',
          langcode: ''
        });
        fetchBrands();
      } else {
        Swal.fire({
          icon: 'error',
          title: 'Oops...',
          text: 'Failed to create brand!',
        });
      }
    } catch (error) {
      console.error("Failed to create brand:", error);
      Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: 'Failed to create brand!',
      });
    }
  };

    const handleEditSubmit = async () => {
    try {
      setLoading(true);

      if (!editForm.id) {
        throw new Error('Brand ID is required');
      
      }
       const { name, url, language, langcode, id } = editForm;
      if (!name || !url || !language || !langcode) {
        Swal.fire({
          icon: 'error',
          title: 'Oops...',
          text: 'Name is required!',
        });
        return;
      }
      const response = await updateBrand({ name, url, language, langcode }, id);
      console.log({ response });
      if (response.success !== true) {
        Swal.fire({
          icon: 'error',
          title: 'Oops...',
          text: 'Failed to update brand!',
        });
        return;
      } 
      Swal.fire({
        icon: 'success',
        title: 'Brand updated successfully!',
        showConfirmButton: false,
        timer: 1500
      });
      setShowEditModal(false);
      setEditingBrand(null);
      setEditForm({ id: '', name: '', url: '', language: '', langcode: '' });
      fetchBrands();

    } catch (error) {
      Swal.fire({
        title: 'Error!',
        text: error.message || 'Failed to update brand',
        icon: 'error',
        timer: 3000,
        showConfirmButton: false
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Get user data from localStorage
  const user = JSON.parse(localStorage.getItem('user') || '{}');

  // New filtering function
  const applyFilter = (value, data) => {
    if (!value.trim()) {
      setFilteredBrands(data); // Show all items when search is empty
      return;
    }

    const filteredData = data.filter(item =>
      item.title?.toLowerCase().includes(value.toLowerCase()) ||
      item.original_text?.toLowerCase().includes(value.toLowerCase()) ||
      item.translate_text?.toLowerCase().includes(value.toLowerCase())
    );
    setFilteredBrands(filteredData);
  };

  // Updated search handler
  const handleSearch = (value) => {
    setSearchTitle(value);
    applyFilter(value, allBrands);
  };

  useEffect(() => {
    fetchBrands();
  }, []);

  const fetchBrands = async () => {
    try {
      setLoading(true);
      const response = await getAllBrands();
      console.log({ response });

      let brandsData = [];
      if (Array.isArray(response)) {
        brandsData = response;
      } else if (response && response.data && Array.isArray(response.data)) {
        brandsData = response.data.flat();
      } else if (response && Array.isArray(response.data)) {
        brandsData = response.data;
      } else if (response && Array.isArray(response.brands)) {
        brandsData = response.brands;
      } else if (response && response.data && Array.isArray(response.data.brands)) {
        brandsData = response.data.brands;
      }
      console.log({ brandsData });


      const filteredData = brandsData
      console.log({filteredData});

      // Store original data
      setAllBrands(filteredData);
      // Update filtered data
      applyFilter(searchTitle, filteredData);
    } catch (error) {
      console.error("Failed to fetch brands:", error);
      setAllBrands([]);
      setFilteredBrands([]);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    const result = await Swal.fire({
      title: 'Are you sure?',
      text: 'You will be logged out of your account',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Yes, logout!'
    });
    if (result.isConfirmed) {
      logoutUser();
      await Swal.fire({
        title: 'Logged out!',
        text: 'You have been successfully logged out.',
        icon: 'success',
        timer: 3000,
        showConfirmButton: false
      });
      navigate('/login');
    }
  };

  const handleBackToDashboard = () => {
    navigate('/dashboard');
  };


  const handleEdit = (brand) => {
    setEditingBrand(brand);
    setEditForm({
      id: brand.id,
      name: brand.name,
      url: brand.url,
      language: brand.language,
      langcode: brand.langcode,
    });
    setShowEditModal(true);
  };

  const handleDelete = async (brand) => {
    if (window.confirm(`Are you sure you want to delete brand ID: ${brand.id}?`)) {
      try {
        setLoading(true);
        await deleteBrands([brand.id]);
        Swal.fire({
          title: 'Success!',
          text: 'Brand deleted successfully',
          icon: 'success',
          timer: 3000,
          showConfirmButton: false
        });
        fetchBrands();
      } catch (error) {
        Swal.fire({
          title: 'Error!',
          text: error.message || 'Failed to delete brand',
          icon: 'error',
          timer: 3000,
          showConfirmButton: false
        });
      } finally {
        setLoading(false);
      }
    }
  };

  const handleSelectAll = (checked) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedItems(filteredBrands.map(t => t.id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (id, checked) => {
    if (checked) {
      setSelectedItems(prev => [...prev, id]);
    } else {
      setSelectedItems(prev => prev.filter(item => item !== id));
      setSelectAll(false);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedItems.length === 0) return;
    if (window.confirm(`Are you sure you want to delete ${selectedItems.length} selected brands?`)) {
      try {
        setLoading(true);
        await deleteBrands(selectedItems);
        Swal.fire({
          title: 'Success!',
          text: `${selectedItems.length} brands deleted successfully`,
          icon: 'success',
          timer: 3000,
          showConfirmButton: false
        });
        setSelectedItems([]);
        setSelectAll(false);
        fetchBrands();
      } catch (error) {
        Swal.fire({
          title: 'Error!',
          text: error.message || 'Failed to delete brands',
          icon: 'error',
          timer: 3000,
          showConfirmButton: false
        });
      } finally {
        setLoading(false);
      }
    }
  };

  
  const handleEditFormChange = (field, value) => {
    setEditForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCreateFormChange = (field, value) => {
    console.log({ field, value });
    setCreateForm(prev => ({
      ...prev,
      [field]: value
    }));
  };


  const handleCreateCancel = () => {
    setShowCreateModal(false);
  };

  const handleEditCancel = () => {
    setShowEditModal(false);
    setEditingBrand(null);
    setEditForm({ id: '', original_text: '', translate_text: '' });
  };


  if (!user.token) {
    navigate('/login');
    return null;
  }

  return (
    <div className="brand-table-page">
      {/* <header className="page-header">
        <div className="header-content">
          <div className="header-left">
            <button className="back-button" onClick={handleBackToDashboard}>
              ← Back to Dashboard
            </button>
            <h1>All Brands</h1>
          </div>
          <div className="header-actions">
            <div className="user-dropdown-container">
              <button
                className="user-button"
                onClick={() => setShowUserDropdown(!showUserDropdown)}
              >
                <div className="user-avatar">V</div>
                <span>{user.name}</span>
                <svg className="dropdown-arrow" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
              {showUserDropdown && (
                <div className="user-dropdown">
                  <div className="dropdown-item">
                    <strong>{user.name}</strong>
                    <small>{user.email}</small>
                  </div>
                  <hr />
                  <button className="dropdown-item" onClick={handleLogout}>
                    Logout
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </header> */}
      <Header />
      <main className="page-main">
        <div className="table-container">
          {loading ? (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>Loading brands...</p>
            </div>
          ) : Array.isArray(filteredBrands) ? (
            <div className="table-wrapper">
              <div className="table-header">
                <div className="table-header-left">
                  <h2>Brands List</h2>
                 <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      backgroundColor: '#e8f0fe',
                      padding: '6px 12px',
                      borderRadius: '10px',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#1a3e9a',
                      width: 'fit-content',
                      boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
                      marginBottom: '10px',
                    }}
                  >
                    <svg
                      width="18"
                      height="18"
                      fill="#1a3e9a"
                      viewBox="0 0 24 24"
                    >
                      <path d="M3 6h18v2H3V6zm0 5h18v2H3v-2zm0 5h12v2H3v-2z" />
                    </svg>
                    <span>Total: {filteredBrands.length} brands</span>
                  </div>
                </div>
                <div className="table-header-right">
                  {selectedItems.length > 0 && (
                    <button
                      className="bulk-delete-btn"
                      onClick={handleBulkDelete}
                      disabled={loading}
                      title={`Delete ${selectedItems.length} selected items`}
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <polyline points="3,6 5,6 21,6" />
                        <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2" />
                        <line x1="10" y1="11" x2="10" y2="17" />
                        <line x1="14" y1="11" x2="14" y2="17" />
                      </svg>
                      {loading ? 'Deleting...' : `Delete All (${selectedItems.length})`}
                    </button>
                  )}
                  <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                    <label htmlFor="title-search">Search:</label>
                    <input
                      id="title-search"
                      type="text"
                      placeholder="Enter text..."
                      value={searchTitle}
                      onChange={(e) => handleSearch(e.target.value)}
                      style={{
                        padding: '6px 10px',
                        fontSize: '14px',
                        borderRadius: '4px',
                        border: '1px solid #ccc',
                        width: '200px'
                      }}
                    />
                  </div>

                  <div style={{ padding: "0 10px"  }}>
                    <button
                      onClick={() => setShowCreateModal(true)}
                      style={{
                        padding: '8px 16px',
                        fontSize: '14px',
                        borderRadius: '4px',
                        border: '1px solid #ccc',
                        width: '200px',
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        color: '#fff',
                      }}
                    >
                      Create New Brand
                    </button>
                  </div>
                 
                </div>
              </div>
              {filteredBrands.length > 0 ? (
                <div className="table-scroll">
                <table className="brands-table">
                  <thead>
                    <tr>
                      <th className="checkbox-header">
                        <input
                          type="checkbox"
                          checked={selectAll}
                          onChange={(e) => handleSelectAll(e.target.checked)}
                          title="Select All"
                        />
                      </th>
                      <th>ID</th>
                      <th>Name</th>
                      <th>Language</th>
                      <th>Language Code</th>
                      <th>URL</th>
                      <th>Created By</th>
                      <th>Updated By</th>
                      <th>Created At</th>
                      <th>Updated At</th>
                      {/* <th>Created Date</th> */}
                      <th className="action-header">Actions</th>

                    </tr>
                  </thead>
                  <tbody>
                    {filteredBrands.map((brand, index) => (
                      <tr key={brand.id || index}>
                        <td className="checkbox-cell">
                          <input
                            type="checkbox"
                            checked={selectedItems.includes(brand.id)}
                            onChange={(e) => handleSelectItem(brand.id, e.target.checked)}
                            title="Select this item"
                          />
                        </td>
                        <td>{brand.id || index + 1}</td>
                        <td className="text-cell">
                          <div className="text-content">
                            {brand.name || 'N/A'}
                          </div>
                        </td>
                        <td className="text-cell">
                          <div
                            className="text-content"
                            >
                            {brand.language || 'N/A'}
                          </div>
                        </td>
                        <td className="text-cell">
                          <div
                            className="text-content"
                            >
                            {brand.langcode || 'N/A'}
                          </div>
                        </td>
                        <td className="text-cell">
                          <div
                            className="text-content"
                            >
                            {brand.url || 'N/A'}
                          </div>
                        </td>
                        <td>
                          <span className="created-by-badge">
                            {brand.created_by.name || 'N/A'}
                          </span>
                        </td>
                        <td>
                          <span className="updated-by-badge">
                            {brand.updated_by?.name || 'N/A'}
                          </span>
                        </td>
           
                
                        <td>
                          {brand.created_at
                            ? new Date(brand.created_at).toLocaleDateString()
                            : brand.createdAt
                              ? new Date(brand.createdAt).toLocaleDateString()
                              : 'N/A'}
                        </td>

                                     <td>
                          <span className={`updated-at-badge ${brand.active === '1' ? 'active' : brand.status || 'active'}`}>
                            {brand.updated_at
                            ? new Date(brand.updated_at).toLocaleDateString()
                            : brand.updatedAt
                              ? new Date(brand.updatedAt).toLocaleDateString()
                              : 'N/A'}
                          </span>
                        </td>
                        <td className="action-cell">
                          <div className="action-buttons">
                            <button
                              className="edit-btn"
                              onClick={() => handleEdit(brand)}
                              title="Edit Brand"
                            >
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                                <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                              </svg>
                            </button>
                            <button
                              className="delete-btn"
                              onClick={() => handleDelete(brand)}
                              title="Delete Brand"
                            >
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <polyline points="3,6 5,6 21,6" />
                                <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2" />
                                <line x1="10" y1="11" x2="10" y2="17" />
                                <line x1="14" y1="11" x2="14" y2="17" />
                              </svg>
                            </button>
                          </div>
                        </td>
                        
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              ) : (
                <div className="no-data-container">
                  <h2>No Brands Found</h2>
                  <p>There are no brands available at the moment.</p>
                  <button className="back-button" onClick={handleBackToDashboard}>
                    Back to Dashboard
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="no-data-container">
              <h2>No Brands Found</h2>
              <p>There are no brands available at the moment.</p>
              <button className="back-button" onClick={handleBackToDashboard}>
                Back to Dashboard
              </button>
            </div>
          )}
        </div>
      </main>

 

      {/* Create Modal */}
      {showCreateModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Create Brand</h3>
              <button className="modal-close" onClick={handleCreateCancel}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18" />
                  <line x1="6" y1="6" x2="18" y2="18" />
                </svg>
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label htmlFor="brand-name">Brand Name:<span style={{color:"red"}}>*</span></label>
                <input
                  type="text"
                  id="brand-name"
                  value={createForm.brand_name}
                  onChange={(e) => handleCreateFormChange('name', e.target.value)}
                  placeholder="Enter brand name"
                  style={{
                    width: '100%',
                    padding: '8px',
                    fontSize: '14px',
                    borderRadius: '4px',
                    border: '1px solid #ccc',
                    marginBottom: '12px',
                  }}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="language">Language:<span style={{color:"red"}}>*</span></label>
                <input
                  type="text"
                  id="language"
                  value={createForm.language}
                  onChange={(e) => handleCreateFormChange('language', e.target.value)}
                  placeholder="Enter language"
                  style={{
                    width: '100%',
                    padding: '8px',
                    fontSize: '14px',
                    borderRadius: '4px',
                    border: '1px solid #ccc',
                    marginBottom: '12px',
                  }}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="langcode">Language Code:<span style={{color:"red"}}>*</span></label>
                <input
                  type="text"
                  id="langcode"
                  value={createForm.langcode}
                  onChange={(e) => handleCreateFormChange('langcode', e.target.value)}
                  placeholder="Enter language code"
                  style={{
                    width: '100%',
                    padding: '8px',
                    fontSize: '14px',
                    borderRadius: '4px',
                    border: '1px solid #ccc',
                    marginBottom: '12px',
                  }}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="brand-url">Brand URL:<span style={{color:"red"}}>*</span></label>
                <input
                  type="text"
                  id="brand-url"
                  value={createForm.brand_url}
                  onChange={(e) => handleCreateFormChange('url', e.target.value)}
                  placeholder="Enter brand url"
                  style={{
                    width: '100%',
                    padding: '8px',
                    fontSize: '14px',
                    borderRadius: '4px',
                    border: '1px solid #ccc',
                    marginBottom: '12px',
                  }}
                  required
                />
              </div>

            </div>
            <div className="modal-footer">
              <button className="cancel-btn" onClick={handleCreateCancel}>
                Cancel
              </button>
              <button className="save-btn" onClick={handleCreateBrand} disabled={loading}>
                {loading ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </div>
        </div>
      )}
      {/* Edit Modal */}
      {showEditModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Edit Brand</h3>
              <button className="modal-close" onClick={handleEditCancel}> 
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18" />
                  <line x1="6" y1="6" x2="18" y2="18" />
                </svg>
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label htmlFor="brand-name">Brand Name:<span style={{color:"red"}}>*</span></label>
                <input
                  type="text"
                  id="brand-name"
                  value={editForm.name}
                  onChange={(e) => handleEditFormChange('name', e.target.value)}
                  placeholder="Enter brand name"
                  style={{
                    width: '100%',
                    padding: '8px',
                    fontSize: '14px',
                    borderRadius: '4px',
                    border: '1px solid #ccc',
                    marginBottom: '12px',
                  }}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="language">Language:<span style={{color:"red"}}>*</span></label>
                <input
                  type="text"
                  id="language"
                  value={editForm.language}
                  onChange={(e) => handleEditFormChange('language', e.target.value)}
                  placeholder="Enter language"
                  style={{
                    width: '100%',
                    padding: '8px',
                    fontSize: '14px',
                    borderRadius: '4px',
                    border: '1px solid #ccc',
                    marginBottom: '12px',
                  }}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="langcode">Language Code:<span style={{color:"red"}}>*</span></label>
                <input
                  type="text"
                  id="langcode"
                  value={editForm.langcode}
                  onChange={(e) => handleEditFormChange('langcode', e.target.value)}
                  placeholder="Enter language code"
                  style={{
                    width: '100%',
                    padding: '8px',
                    fontSize: '14px',
                    borderRadius: '4px',
                    border: '1px solid #ccc',
                    marginBottom: '12px',
                  }}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="brand-url">Brand URL:<span style={{color:"red"}}>*</span></label>
                <input
                  type="text"
                  id="brand-url"
                  value={editForm.url}
                  onChange={(e) => handleEditFormChange('url', e.target.value)}
                  placeholder="Enter brand url"
                  style={{
                    width: '100%',
                    padding: '8px',
                    fontSize: '14px',
                    borderRadius: '4px',
                    border: '1px solid #ccc',
                    marginBottom: '12px',
                  }}
                  required
                />
              </div>

            </div>
            <div className="modal-footer">
              <button className="cancel-btn" onClick={handleEditCancel}>
                Cancel
              </button>
              <button className="save-btn" onClick={handleEditSubmit} disabled={loading}>
                {loading ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BrandTablePage;