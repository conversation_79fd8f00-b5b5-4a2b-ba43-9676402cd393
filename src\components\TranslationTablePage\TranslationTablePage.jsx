import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Swal from 'sweetalert2';
import { Editor } from 'primereact/editor';
import '@uiw/react-md-editor/markdown-editor.css';
import { getAllTranslations, addTranslation, logoutUser, copyTranslation, updateTranslation, deleteTranslations, getAllBrands, resyncTranslation } from '../../ServiceApi/Api';
import './TranslationTablePage.css';
import Header from '../Header';
// import SyncIcon from '@mui/icons-material/SyncAlt';

const TranslationTablePage = () => {
  const [allTranslations, setAllTranslations] = useState([]); // Original data
  const [filteredTranslations, setFilteredTranslations] = useState([]); // Displayed data
  const [loading, setLoading] = useState(true);
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const [brands, setBrands] = useState([]); // Dynamic brand list
  const [selectedBrand, setSelectedBrand] = useState('');
  const [selectedItems, setSelectedItems] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showConvertModal, setShowConvertModal] = useState(false);
  const [convertBrands, setConvertBrands] = useState([]);
  const [editingTranslation, setEditingTranslation] = useState(null);
  const [searchTitle, setSearchTitle] = useState('');
  const [editForm, setEditForm] = useState({
    id: '',
    original_text: '',
    translate_text: ''
  });
  const navigate = useNavigate();
  const [isResyncing, setIsResyncing] = useState(false);

  const handleResync = async () => {
    setIsResyncing(true);
    try {
      await resyncTranslation(selectedBrand);
      setTimeout(() => {
        setIsResyncing(false);
      }, 3000);
      Swal.fire({
        title: 'Success!',
        text: 'Translations resynced successfully',
        icon: 'success',
        timer: 3000,
        showConfirmButton: false
      });
      fetchTranslations();
    } catch (error) {
      Swal.fire({
        title: 'Error!',
        text: error.message || 'Failed to resync translations',
        icon: 'error',
        timer: 3000,
        showConfirmButton: false
      });
    } finally {
      setTimeout(() => {
        setIsResyncing(false);
      }, 3000);
    }
  };

  // Get user data from localStorage
  const user = JSON.parse(localStorage.getItem('user') || '{}');

  // New filtering function
  const applyFilter = (value, data) => {
    if (!value.trim()) {
      setFilteredTranslations(data); // Show all items when search is empty
      return;
    }

    const filteredData = data.filter(item =>
      item.title?.toLowerCase().includes(value.toLowerCase()) ||
      item.original_text?.toLowerCase().includes(value.toLowerCase()) ||
      item.translate_text?.toLowerCase().includes(value.toLowerCase())
    );
    setFilteredTranslations(filteredData);
  };

  // Updated search handler
  const handleSearch = (value) => {
    setSearchTitle(value);
    applyFilter(value, allTranslations);
  };

  // Fetch brands on mount
  useEffect(() => {
    const fetchBrands = async () => {
      try {
        const res = await getAllBrands();
        const brandList = Array.isArray(res.data) ? res.data : [];
        setBrands(brandList);
        if (!selectedBrand && brandList.length > 0) {
          setSelectedBrand(brandList[0].name);
        }
      } catch (err) {
        setBrands([]);
        console.error('Error fetching brands:', err);
      }
    };
    fetchBrands();
  }, []);

  useEffect(() => {
    if (selectedBrand) fetchTranslations();
  }, [selectedBrand]);

  const fetchTranslations = async () => {
    try {
      setLoading(true);
      const response = await getAllTranslations(selectedBrand);
      console.log({ response });

      let translationsData = [];
      if (Array.isArray(response)) {
        translationsData = response;
      } else if (response && response.data && Array.isArray(response.data)) {
        translationsData = response.data.flat();
      } else if (response && Array.isArray(response.data)) {
        translationsData = response.data;
      } else if (response && Array.isArray(response.translations)) {
        translationsData = response.translations;
      } else if (response && response.data && Array.isArray(response.data.translations)) {
        translationsData = response.data.translations;
      }
      console.log({ translationsData });


      const filteredData = translationsData.filter(translation =>
        translation.brand && translation.brand.toLowerCase() === selectedBrand.toLowerCase()
      );
      console.log({ filteredData });

      // Store original data
      setAllTranslations(filteredData);
      // Update filtered data
      applyFilter(searchTitle, filteredData);
    } catch (error) {
      console.error("Failed to fetch translations:", error);
      setAllTranslations([]);
      setFilteredTranslations([]);
    } finally {
      setLoading(false);
    }
  };

  const handleBackToDashboard = () => {
    navigate('/dashboard');
  };

  // const handleResync = async () => {
  //   try {
  //     setLoading(true);
  //     await resyncTranslation();
  //     Swal.fire({
  //       title: 'Success!',
  //       text: 'Translations resynced successfully',
  //       icon: 'success',
  //       timer: 3000,
  //       showConfirmButton: false
  //     });
  //     fetchTranslations();
  //   } catch (error) {
  //     Swal.fire({
  //       title: 'Error!',
  //       text: error.message || 'Failed to resync translations',
  //       icon: 'error',
  //       timer: 3000,
  //       showConfirmButton: false
  //     });
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  const handleBrandChange = (event) => {
    setSelectedBrand(event.target.value);
    setSelectedItems([]);
  };

  const handleEdit = (translation) => {
    setEditingTranslation(translation);
    // console.log({translation, brands, selectedBrand});
    const title = translation.title;
    const originalText = translation.original_text.replaceAll(translation.url, '{{link}}');
    const translateText = translation.translate_text.replaceAll(translation.url, '{{link}}') || '';
    setEditForm({
      id: translation.id,
      title: title,
      original_text: originalText,
      translate_text: translateText
    });
    setShowEditModal(true);
  };

  const handleDelete = async (translation) => {
    if (window.confirm(`Are you sure you want to delete translation ID: ${translation.id}?`)) {
      try {
        setLoading(true);
        await deleteTranslations([translation.id]);
        Swal.fire({
          title: 'Success!',
          text: 'Translation deleted successfully',
          icon: 'success',
          timer: 3000,
          showConfirmButton: false
        });
        fetchTranslations();
      } catch (error) {
        Swal.fire({
          title: 'Error!',
          text: error.message || 'Failed to delete translation',
          icon: 'error',
          timer: 3000,
          showConfirmButton: false
        });
      } finally {
        setLoading(false);
      }
    }
  };

  const handleSelectAll = (checked) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedItems(filteredTranslations.map(t => t.id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (id, checked) => {
    if (checked) {
      setSelectedItems(prev => [...prev, id]);
    } else {
      setSelectedItems(prev => prev.filter(item => item !== id));
      setSelectAll(false);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedItems.length === 0) return;
    if (window.confirm(`Are you sure you want to delete ${selectedItems.length} selected translations?`)) {
      try {
        setLoading(true);
        await deleteTranslations(selectedItems);
        Swal.fire({
          title: 'Success!',
          text: `${selectedItems.length} translations deleted successfully`,
          icon: 'success',
          timer: 3000,
          showConfirmButton: false
        });
        setSelectedItems([]);
        setSelectAll(false);
        fetchTranslations();
      } catch (error) {
        Swal.fire({
          title: 'Error!',
          text: error.message || 'Failed to delete translations',
          icon: 'error',
          timer: 3000,
          showConfirmButton: false
        });
      } finally {
        setLoading(false);
      }
    }
  };

  // Convert to Brand logic
  const handleConvertToBrand = () => {
    // Exclude the current brand from the modal
    setConvertBrands([]);
    setShowConvertModal(true);
  };

  const handleConvertBrandChange = (brand, checked) => {
    setConvertBrands(prev => {
      if (checked) {
        return [...prev, brand];
      } else {
        return prev.filter(b => b !== brand);
      }
    });
  };

  const handleConvertSubmit = async () => {
    if (convertBrands.length === 0 || selectedItems.length === 0) return;
    setLoading(true);
    // Build brand name to ID map from brands state
    const brandNameToId = brands.reduce((acc, b) => { acc[b.name] = b.id; return acc; }, {});
    try {
      // Find selected translations
      const selectedTranslations = filteredTranslations.filter(t => selectedItems.includes(t.id));
      let createdCount = 0;
      const brandIds = convertBrands.map(b => brandNameToId[b]).filter(Boolean);
      for (const translation of selectedTranslations) {
        // Avoid duplicating in the same brand (skip if translation.brand is in selected brands)
        if (translation.brand && brandIds.includes(brandNameToId[translation.brand.toLowerCase()])) continue;
        const createData = {
          current_id: translation.id || '',
          current_brand: brands.find(brand => brand.name === selectedBrand)?.id,
          // original_text: translation.original_text || translation.originalText,
          brands: brandIds
        };
        await copyTranslation(createData);
        createdCount++;
      }
      Swal.fire({
        title: 'Success!',
        text: `${createdCount} translation(s) converted to selected brand(s)`,
        icon: 'success',
        timer: 3000,
        showConfirmButton: false
      });
      setShowConvertModal(false);
      setConvertBrands([]);
      setSelectedItems([]); // Remove selected checkboxes after success
      setSelectAll(false);
      fetchTranslations();
    } catch (error) {
      Swal.fire({
        title: 'Error!',
        text: error.message || 'Failed to convert translations',
        icon: 'error',
        timer: 3000,
        showConfirmButton: false
      });
    } finally {
      setLoading(false);
    }
  };

  const handleConvertCancel = () => {
    setShowConvertModal(false);
    setConvertBrands([]);
  };

  const handleEditFormChange = (field, value) => {
    setEditForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCreateSubmit = async () => {
    try {
      setLoading(true);

      const originalText = editForm.original_text?.trim();
      if (!originalText) {
        throw new Error('Original text is required');
      }

      // Get target language from the editing translation
      const targetLang = editingTranslation?.target_lang || 'tr'; // Default to 'tr' if missing

      // Call generateTranslation API
      const response = await generateTranslation({
        text: originalText,
        source_lang: 'en',
        target_lang: [targetLang]
      });

      const translatedText = response?.translations?.[0]?.text?.trim();
      if (!translatedText) {
        throw new Error('No translated text returned from API');
      }

      const createData = {
        original_text: originalText,
        translate_text: translatedText
      };

      await addTranslation(createData);

      Swal.fire({
        title: 'Success!',
        text: 'Translation created successfully',
        icon: 'success',
        timer: 3000,
        showConfirmButton: false
      });

      setShowEditModal(false);
      setEditingTranslation(null);
      setEditForm({ id: '', original_text: '', translate_text: '' });
      fetchTranslations();

    } catch (error) {
      Swal.fire({
        title: 'Error!',
        text: error.message || 'Failed to update translation',
        icon: 'error',
        timer: 3000,
        showConfirmButton: false
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditSubmit = async () => {
    try {
      setLoading(true);
      if (!editForm.id) {
        throw new Error('Translation ID is required');
      }
      const title = editForm.title;
      const brand = editForm.brand || selectedBrand;
      const originalText = editForm.original_text?.trim();
      if (!originalText) {
        throw new Error('Original text is required');
      }

      // Find the brand object to get its id
      const brandObj = brands.find(b => b.name === brand);
      const brandId = brandObj ? brandObj.id : null;
      const updateData = {
        id: parseInt(editForm.id),
        title: title,
        brands: brandId ? [brandId] : [],
        original_text: originalText
      };

      await updateTranslation(updateData, editForm.id);

      Swal.fire({
        title: 'Success!',
        text: 'Translation updated successfully',
        icon: 'success',
        timer: 3000,
        showConfirmButton: false
      });

      setShowEditModal(false);
      setEditingTranslation(null);
      setEditForm({ id: '', original_text: '', translate_text: '' });
      fetchTranslations();

    } catch (error) {
      Swal.fire({
        title: 'Error!',
        text: error.message || 'Failed to update translation',
        icon: 'error',
        timer: 3000,
        showConfirmButton: false
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCancel = () => {
    setShowCreateModal(false);
  };

  const handleEditCancel = () => {
    setShowEditModal(false);
    setEditingTranslation(null);
    setEditForm({ id: '', original_text: '', translate_text: '' });
  };

  const convertToPlainText = (content) => {
    if (!content) return '';
    if (typeof content === 'string' && !content.includes('<')) {
      return content.trim();
    }
    try {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = content;
      let plainText = tempDiv.textContent || tempDiv.innerText || '';
      plainText = plainText.replace(/\\\\n\\\\s*\\\\n/g, '\\\\n\\\\n');
      plainText = plainText.replace(/\\\\s+/g, ' ');
      plainText = plainText.trim();
      return plainText;
    } catch (error) {
      console.error('Error converting to plain text:', error);
      return content.toString().trim();
    }
  };

  if (!user.token) {
    navigate('/login');
    return null;
  }

  return (
    <div className="translation-table-page">
      {/* <header className="page-header">
        <div className="header-content">
          <div className="header-left">
            <button className="back-button" onClick={handleBackToDashboard}>
              ← Back to Dashboard
            </button>
            <h1>All Translations</h1>
          </div>
          <div className="header-actions">
            <div className="user-dropdown-container">
              <button
                className="user-button"
                onClick={() => setShowUserDropdown(!showUserDropdown)}
              >
                <div className="user-avatar">V</div>
                <span>{user.name}</span>
                <svg className="dropdown-arrow" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
              {showUserDropdown && (
                <div className="user-dropdown">
                  <div className="dropdown-item">
                    <strong>{user.name}</strong>
                    <small>{user.email}</small>
                  </div>
                  <hr />
                  <button className="dropdown-item" onClick={handleLogout}>
                    Logout
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </header> */}
      <Header />
      <main className="page-main">
        <div className="table-container">
          {loading ? (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>Loading translations...</p>
            </div>
          ) : Array.isArray(filteredTranslations) ? (
            <div className="table-wrapper">
              <div className="table-header">
                <div className="table-header-left">
                  <h2>Translation History</h2>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      backgroundColor: '#e8f0fe',
                      padding: '6px 12px',
                      borderRadius: '10px',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#1a3e9a',
                      width: 'fit-content',
                      boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
                      marginBottom: '10px',
                    }}
                  >
                    <svg
                      width="18"
                      height="18"
                      fill="#1a3e9a"
                      viewBox="0 0 24 24"
                    >
                      <path d="M3 6h18v2H3V6zm0 5h18v2H3v-2zm0 5h12v2H3v-2z" />
                    </svg>
                    <span>Total: {filteredTranslations.length} translations</span>
                  </div>

                </div>
                <div className="table-header-right">
                  {selectedItems.length > 0 && (
                    <>
                      <button
                        className="bulk-delete-btn"
                        onClick={handleBulkDelete}
                        disabled={loading}
                        title={`Delete ${selectedItems.length} selected items`}
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <polyline points="3,6 5,6 21,6" />
                          <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2" />
                          <line x1="10" y1="11" x2="10" y2="17" />
                          <line x1="14" y1="11" x2="14" y2="17" />
                        </svg>
                        {loading ? 'Deleting...' : `Delete All (${selectedItems.length})`}
                      </button>
                      <button
                        className="convert-btn"
                        style={{ margin: '0 10px', background: '#4caf50', color: 'white', border: 'none', borderRadius: '5px', padding: '6px 12px', cursor: 'copy' }}
                        onClick={handleConvertToBrand}
                        disabled={loading}
                        title="Convert selected to another brand"
                      >
                        Copy to Brand
                      </button>
                    </>
                  )}
                  {/* Convert to Brand Modal */}
                  {showConvertModal && (
                    <div className="modal-overlay">
                      <div className="modal-content">
                        <div className="modal-header">
                          <h3>Convert to Brand(s)</h3>
                          <button className="modal-close" onClick={handleConvertCancel}>
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <line x1="18" y1="6" x2="6" y2="18" />
                              <line x1="6" y1="6" x2="18" y2="18" />
                            </svg>
                          </button>
                        </div>
                        <div className="modal-body">
                          <div className="form-group">
                            <label>Select Brand(s):</label>
                            <div style={{ display: 'flex', gap: '20px', margin: '10px 0' }}>
                              {brands.filter(brand => brand.name !== selectedBrand).map(brand => (
                                <label key={brand.id}>
                                  <input
                                    type="checkbox"
                                    checked={convertBrands.includes(brand.name)}
                                    onChange={e => handleConvertBrandChange(brand.name, e.target.checked)}
                                  /> {brand.name.charAt(0).toUpperCase() + brand.name.slice(1)}
                                </label>
                              ))}
                            </div>
                            <p style={{ fontSize: '13px', color: '#888' }}>Selected translations will be copied to the selected brand(s).</p>
                          </div>
                        </div>
                        <div className="modal-footer">
                          <button className="cancel-btn" onClick={handleConvertCancel}>
                            Cancel
                          </button>
                          <button className="save-btn" onClick={handleConvertSubmit} disabled={loading || convertBrands.length === 0}>
                            {loading ? 'Converting...' : 'Convert'}
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                  {filteredTranslations.length > 0 && (
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        margin: '0 10px',
                      }}
                    >
                      <button
                        id="re-sync"
                        onClick={handleResync}
                        title="Resync translations"
                        style={{
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          padding: '6px',
                          fontSize: '14px',
                          borderRadius: '10px',
                          border: '1px solid #ddd',
                          backgroundColor: '#fff',
                          boxShadow: '0 1px 3px #2947e2',
                          cursor: 'pointer',
                          transition: 'transform 0.2s ease, box-shadow 0.2s ease, background-color 0.3s ease',
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform = 'scale(1.05)';
                          e.currentTarget.style.boxShadow = '0 2px 6px #2947e2';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform = 'scale(1)';
                          e.currentTarget.style.boxShadow = '0 1px 3px #2947e2';
                        }}
                        onMouseDown={(e) => {
                          e.currentTarget.style.transform = 'scale(0.95)';
                        }}
                        onMouseUp={(e) => {
                          e.currentTarget.style.transform = 'scale(1.05)';
                        }}
                      >
                        <svg
                          width="25"
                          height="25"
                          viewBox="0 0 48 48"
                          xmlns="http://www.w3.org/2000/svg"
                          style={{
                            animation: isResyncing ? 'spin 2s linear infinite' : 'none',
                          }}
                        >
                          <path
                            fill="#2947e2"
                            d="M38.7,11.9l-3.1,2.5c2.2,2.7,3.4,6.1,3.4,9.5c0,8.3-6.7,15-15,15c-0.9,0-1.9-0.1-2.8-0.3l-0.7,3.9 
c1.2,0.2,2.4,0.3,3.5,0.3c10.5,0,19-8.5,19-19C43,19.6,41.5,15.3,38.7,11.9z"
                          />
                          <polygon fill="#FF6F02" points="31,8 42.9,9.6 33.1,19.4" />
                          <path
                            fill="#2947e2"
                            d="M24,5C13.5,5,5,13.5,5,24c0,4.6,1.6,9,4.6,12.4l3-2.6C10.3,31.1,9,27.6,9,24c0-8.3,6.7-15,15-15 
c0.9,0,1.9,0.1,2.8,0.3l0.7-3.9C26.4,5.1,25.2,5,24,5z"
                          />
                          <polygon fill="#FF6F02" points="17,40 5.1,38.4 14.9,28.6" />
                        </svg>
                      </button>
                    </div>

                  )}
                  <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                    <label htmlFor="title-search">Search:</label>
                    <input
                      id="title-search"
                      type="text"
                      placeholder="Enter text..."
                      value={searchTitle}
                      onChange={(e) => handleSearch(e.target.value)}
                      style={{
                        padding: '6px 10px',
                        fontSize: '14px',
                        borderRadius: '4px',
                        border: '1px solid #ccc',
                        width: '200px'
                      }}
                    />
                  </div>
                  <div className="brand-filter" style={{ padding: '0 10px' }}>
                    <label htmlFor="brand-select">Select Brand:</label>
                    <select
                      id="brand-select"
                      value={selectedBrand}
                      onChange={handleBrandChange}
                      className="brand-dropdown"
                    >
                      {brands.map(brand => (
                        <option key={brand.id} value={brand.name}>{brand.name.charAt(0).toUpperCase() + brand.name.slice(1)}</option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
              {filteredTranslations.length > 0 ? (
                <div className="table-scroll">
                  <table className="translations-table">
                    <thead>
                      <tr>
                        <th className="checkbox-header">
                          <input
                            type="checkbox"
                            checked={selectAll}
                            onChange={(e) => handleSelectAll(e.target.checked)}
                            title="Select All"
                          />
                        </th>
                        <th>S.No</th>
                        <th>Title</th>
                        <th>Original Text</th>
                        <th>Translated Text</th>
                        <th>Related Brand</th>
                        <th>Created By</th>
                        <th>Updated By</th>
                        <th>Created At</th>
                        <th>Updated At</th>
                        {/* <th>Created Date</th> */}
                        <th className="action-header">Actions</th>

                      </tr>
                    </thead>
                    <tbody>
                      {filteredTranslations.map((translation, index) => {
                        // console.log({ translation });
                        return (
                          <tr key={translation.id || index}>
                            <td className="checkbox-cell">
                              <input
                                type="checkbox"
                                checked={selectedItems.includes(translation.id)}
                                onChange={(e) => handleSelectItem(translation.id, e.target.checked)}
                                title="Select this item"
                              />
                            </td>
                            <td>{index + 1}</td>
                            <td className="text-cell">
                              <div className="text-content" >
                                {translation.title}
                              </div>
                            </td>
                            <td className="text-cell">
                              <div
                                className="text-content"
                                style={{ resize: 'both' }}
                                dangerouslySetInnerHTML={{
                                  __html: translation.original_text || translation.originalText || 'N/A'
                                }}
                              />
                            </td>
                            <td className="text-cell">
                              <div
                                className="text-content"
                                style={{ resize: 'both' }}
                                dangerouslySetInnerHTML={{
                                  __html: translation.translate_text || translation.translated_text || translation.translatedText || 'N/A'
                                }}
                              />
                            </td>
                            <td>
                              <div className="related-brands">
                                {translation.related_brands?.length > 0 ? (
                                  translation.related_brands.map((brand, idx) => (
                                    <div
                                      key={idx}
                                      className={`brand-letter ${brand.toLowerCase()}`}
                                      data-title={brand}
                                    >
                                      {brand[0].toUpperCase()}
                                    </div>
                                  ))
                                ) : (
                                  <span className="no-brands">--</span>
                                )}
                              </div>
                            </td>

                            <td>
                              <span className="created-by-badge">
                                {translation.created_by?.name || '--'}
                              </span>
                            </td>
                            <td>
                              <span className="updated-by-badge">
                                {translation.updated_by?.name || '--'}
                              </span>
                            </td>

                            <td>
                              {translation.created_at
                                ? new Date(translation.created_at).toLocaleDateString()
                                : translation.createdAt
                                  ? new Date(translation.createdAt).toLocaleDateString()
                                  : '--'}
                            </td>
                            <td>
                              <span className={`updated-at-badge ${translation.active === '1' ? 'active' : translation.status || 'active'}`}>
                                {translation.updated_at
                                  ? new Date(translation.updated_at).toLocaleDateString()
                                  : translation.updatedAt
                                    ? new Date(translation.updatedAt).toLocaleDateString()
                                    : '--'}
                              </span>
                            </td>
                            <td className="action-cell">
                              <div className="action-buttons">
                                <button
                                  className="edit-btn"
                                  onClick={() => handleEdit(translation)}
                                  title="Edit Translation"
                                >
                                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                                    <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                                  </svg>
                                </button>
                                <button
                                  className="delete-btn"
                                  onClick={() => handleDelete(translation)}
                                  title="Delete Translation"
                                >
                                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                    <polyline points="3,6 5,6 21,6" />
                                    <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2" />
                                    <line x1="10" y1="11" x2="10" y2="17" />
                                    <line x1="14" y1="11" x2="14" y2="17" />
                                  </svg>
                                </button>
                              </div>
                            </td>
                          </tr>
                        )
                      })}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="no-data-container">
                  <h2>No Translations Found</h2>
                  <p>There are no translations available at the moment.</p>
                  <button className="back-button" onClick={handleBackToDashboard}>
                    Back to Dashboard
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="no-data-container">
              <h2>No Translations Found</h2>
              <p>There are no translations available at the moment.</p>
              <button className="back-button" onClick={handleBackToDashboard}>
                Back to Dashboard
              </button>
            </div>
          )}
        </div>
      </main>

      {/* Edit Modal */}
      {showEditModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Edit Translation</h3>
              <button className="modal-close" onClick={handleEditCancel}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18" />
                  <line x1="6" y1="6" x2="18" y2="18" />
                </svg>
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label htmlFor="edit-title">Title:</label>
                <input
                  id="edit-title"
                  type="text"
                  value={editForm.title || ''}
                  onChange={e => handleEditFormChange('title', e.target.value)}
                  placeholder="Enter a title"
                  style={{ width: '100%', padding: '8px', fontSize: '14px', borderRadius: '4px', border: '1px solid #ccc', marginBottom: '12px' }}
                />
              </div>
              <div className="form-group">
                <label htmlFor="original-text">Original Text:</label>
                <div className="md-editor-container">
                  <Editor
                    value={editForm.original_text || ''}
                    onTextChange={(e) => handleEditFormChange('original_text', e.htmlValue || '')}
                    style={{ height: '200px' }}
                    headerTemplate={
                      <span className="ql-formats">
                        <button className="ql-bold" title="Bold"></button>
                        <button className="ql-italic" title="Italic"></button>
                        <button className="ql-underline" title="Underline"></button>
                        <button className="ql-link" title="Insert Link"></button>
                      </span>
                    }
                  />

                </div>
              </div>
              <div className="form-group" style={{ display: 'none' }}>
                <label htmlFor="translate-text">Translated Text:</label>
                <div
                  id="translate-text"
                  className="form-textarea"
                  style={{ minHeight: '100px', padding: '8px', border: '1px solid #ccc', borderRadius: '4px' }}
                  dangerouslySetInnerHTML={{ __html: editForm.translate_text }}
                />
              </div>
            </div>
            <div className="modal-footer">
              <button className="cancel-btn" onClick={handleEditCancel}>
                Cancel
              </button>
              <button className="save-btn" onClick={handleEditSubmit} disabled={loading}>
                {loading ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create Modal */}
      {showCreateModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Edit Translation</h3>
              <button className="modal-close" onClick={handleCreateCancel}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18" />
                  <line x1="6" y1="6" x2="18" y2="18" />
                </svg>
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label htmlFor="original-text">Original Text:</label>
                <div className="md-editor-container">
                  <Editor
                    value={editForm.original_text || ''}
                    onTextChange={(e) => handleEditFormChange('original_text', e.htmlValue || '')}
                    style={{ height: '200px' }}
                    headerTemplate={
                      <span className="ql-formats">
                        <button className="ql-bold" title="Bold"></button>
                        <button className="ql-italic" title="Italic"></button>
                        <button className="ql-underline" title="Underline"></button>
                        <button className="ql-link" title="Insert Link"></button>
                      </span>
                    }
                  />

                </div>
              </div>
              <div className="form-group" style={{ display: 'none' }}>
                <label htmlFor="translate-text">Translated Text:</label>
                <div
                  id="translate-text"
                  className="form-textarea"
                  style={{ minHeight: '100px', padding: '8px', border: '1px solid #ccc', borderRadius: '4px' }}
                  dangerouslySetInnerHTML={{ __html: editForm.translate_text }}
                />
              </div>
            </div>
            <div className="modal-footer">
              <button className="cancel-btn" onClick={handleCreateCancel}>
                Cancel
              </button>
              <button className="save-btn" onClick={handleCreateSubmit} disabled={loading}>
                {loading ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TranslationTablePage;