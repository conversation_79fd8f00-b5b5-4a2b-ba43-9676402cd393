{"name": "techrise-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^44.3.0", "@ckeditor/ckeditor5-react": "^9.5.0", "@mui/icons-material": "^7.2.0", "@syncfusion/ej2-react-richtexteditor": "^29.2.11", "@tiptap/extension-bold": "^2.22.3", "@tiptap/extension-italic": "^2.22.3", "@tiptap/extension-link": "^2.22.3", "@tiptap/extension-underline": "^2.22.3", "@tiptap/react": "^2.22.3", "@tiptap/starter-kit": "^2.22.3", "@uiw/react-md-editor": "^4.0.7", "axios": "^1.10.0", "bootstrap": "^5.3.7", "primeflex": "^4.0.0", "primeicons": "^7.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.26.2", "react-transition-group": "^4.4.5", "sweetalert2": "^11.22.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "vite": "^5.4.10"}}