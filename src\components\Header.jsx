// src/components/Header.jsx
import React, { useState, useEffect } from 'react';
import '../App.css'; // optional, for custom header styles
import { useNavigate } from 'react-router-dom';
import { logoutUser } from '../ServiceApi/Api';


const Header = () => {

    const [user, setUser] = useState(null);
    const [showUserDropdown, setShowUserDropdown] = useState(false);
    const slug = window.location.pathname.split('/')[1];
    // console.log(slug);
    const [activeTab, setActiveTab] = useState(slug);
    const navigate = useNavigate();


    useEffect(() => {
        const userData = localStorage.getItem('user');
        if (!userData) {
            navigate('/login');
        }
        setUser(JSON.parse(userData));
    }, [navigate]);

    const handleTabClick = (tab) => {
        setActiveTab(tab);
        if (tab === 'dashboard') {
            navigate('/dashboard');
        } else if (tab === 'brands') {
            navigate('/brands');
        } else if (tab === 'translation-table') {
            navigate('/translation-table');
        } else if (tab === 'glossary') {
            navigate('/glossary');
        }
    };  


    const handleUpdateTranslations = () => {
        navigate('/translation-table');
    };
    const handleBrands = () => {
        navigate('/brands');
    };
    const handleDashboard = () => {
        navigate('/dashboard');
    };
    const handleGlossary = () => {
        navigate('/glossary');
    };

    const handleLogout = () => {
        localStorage.removeItem("user");
        localStorage.clear(); // Clear all localStorage data
        navigate('/login')
        window.location.reload();
    };
    const handleUsers = () => {
        navigate('/user');
    }
    const handleUploads = () => {
        navigate('/uploads');
    }

    if (!user) {
        return <div className="loading">Loading...</div>;
    }
    return (
        <header className="dashboard-header">
            <div className="header-content">
                <h1>Translator Dashboard</h1>
                <div className="header-actions">
                    <button className={`update-translations-btn ${activeTab === 'dashboard' ? 'active' : ''}`} onClick={() => handleTabClick('dashboard')}>Dashboard</button>
                    <button className={`update-translations-btn ${activeTab === 'brands' ? 'active' : ''}`} onClick={() => handleTabClick('brands')}>Brands</button>
                    <button className={`update-translations-btn ${activeTab === 'translation-table' ? 'active' : ''}`} onClick={() => handleTabClick('translation-table')}>Translations</button>
                    <button className={`update-translations-btn ${activeTab === 'glossary' ? 'active' : ''}`} onClick={() => handleTabClick('glossary')}>Glossary</button>

                    <div className="user-dropdown-container">
                        <button className="user-button" onClick={() => setShowUserDropdown(!showUserDropdown)}>
                            <div className="user-avatar">V</div>
                            <span>{user?.name}</span>
                        </button>
                        {showUserDropdown && (
                            <div className="user-dropdown">
                                <div className="dropdown-item">
                                    <strong>{user?.name}</strong>
                                    <small>{user?.email}</small>
                                </div>
                                <hr />
                                <button className="dropdown-item" onClick={handleUsers}>All Users</button>
                                <button className="dropdown-item" onClick={handleUploads}>File Uploads</button>
                                <button className="dropdown-item" onClick={handleLogout}>Logout</button>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </header>
    );
};

export default Header;
