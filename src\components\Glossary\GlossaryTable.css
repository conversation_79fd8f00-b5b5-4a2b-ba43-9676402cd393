.glossary-table-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.page-main {
  flex: 1;
  padding: 24px;
  display: flex;
  justify-content: center;
}

.table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 1600px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 120px);
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 80px 20px;
  color: #666;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #4285f4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.table-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.table-header {
  padding: 24px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.table-header-left {
  flex: 1;
}

.table-header-right {
  display: flex;
  align-items: center;
}

.table-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.create-glossary-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.create-glossary-btn:hover {
  background: #218838;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.create-glossary-btn svg {
  width: 16px;
  height: 16px;
}

/* Bulk Delete Button */
.bulk-delete-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.bulk-delete-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.bulk-delete-btn svg {
  width: 16px;
  height: 16px;
}

.bulk-delete-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.bulk-delete-btn:disabled:hover {
  background: #6c757d;
  transform: none;
  box-shadow: none;
}

/* Checkbox Styles */
.checkbox-header, .checkbox-cell {
  width: 40px;
  text-align: center;
  padding: 8px !important;
}

.checkbox-header input[type="checkbox"],
.checkbox-cell input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
  accent-color: #667eea;
}

.table-scroll {
  flex: 1;
  overflow: auto;
}

.glossary-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.glossary-table th {
  background: #f8f9fa;
  color: #333;
  font-weight: 600;
  padding: 16px;
  text-align: left;
  border-bottom: 2px solid #e9ecef;
  position: sticky;
  top: 0;
  z-index: 10;
}

.glossary-table td {
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
  vertical-align: top;
}

.glossary-table tr:hover {
  background: #f8f9fa;
}

.text-cell {
  max-width: 350px;
}

.text-content {
  max-height: 100px;
  overflow: auto;
  line-height: 1.4;
  word-wrap: break-word;
}

.language-badge {
  background: #4285f4;
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.action-header {
  text-align: center !important;
  width: 120px;
}

.action-cell {
  padding: 12px 8px !important;
  text-align: center;
  white-space: nowrap;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

.edit-btn, .delete-btn {
  padding: 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-btn svg, .delete-btn svg {
  width: 16px;
  height: 16px;
}

.edit-btn {
  background: #28a745;
  color: white;
}

.edit-btn:hover {
  background: #218838;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.delete-btn {
  background: #dc3545;
  color: white;
}

.delete-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.no-data-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 80px 20px;
  text-align: center;
}

.no-data-container h2 {
  color: #333;
  margin-bottom: 16px;
  font-size: 24px;
  font-weight: 600;
}

.no-data-container p {
  color: #666;
  margin-bottom: 24px;
  font-size: 16px;
}

.back-button {
  background: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.back-button:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease;
  display: flex;
  flex-direction: column;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.modal-header h3 {
  margin: 0;
  color: #495057;
  font-size: 18px;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #6c757d;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #e9ecef;
  color: #495057;
}

.modal-body {
  padding: 24px;
  flex: 1;
  overflow-y: auto;
}

.modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: #f8f9fa;
}

/* Language Selection Section */
.language-selection-section {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: 20px;
  margin-bottom: 30px;
  align-items: end;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.form-group {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.required-field {
  color: #dc3545;
}

.form-select {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
}

.form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-select:hover {
  border-color: #667eea;
}

.add-pair-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.add-pair-btn:hover:not(:disabled) {
  background: #218838;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.add-pair-btn svg {
  width: 16px;
  height: 16px;
}

.add-pair-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

/* Edit Text Section */
.edit-text-section {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.edit-text-section .form-group {
  margin-bottom: 20px;
}

.edit-text-section .form-group:last-child {
  margin-bottom: 0;
}

/* Language Pairs Table Section */
.language-pairs-section {
  margin-bottom: 20px;
}

.section-title {
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.pairs-count {
  color: #667eea;
  font-weight: 500;
}

.pairs-table-container {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
}

.pairs-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.pairs-table th {
  background: #f8f9fa;
  color: #333;
  font-weight: 600;
  padding: 12px 16px;
  text-align: left;
  border-bottom: 2px solid #e9ecef;
  position: sticky;
  top: 0;
  z-index: 10;
}

.pairs-table td {
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
  vertical-align: top;
}

.pairs-table tr:nth-child(even) {
  background: #f8f9fa;
}

.pairs-table tr:nth-child(odd) {
  background: #fff;
}

.language-label {
  font-weight: 600;
  margin-bottom: 8px;
  color: #495057;
  font-size: 14px;
}

.text-input {
  width: 100%;
  min-height: 80px;
  padding: 8px 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  resize: vertical;
  font-size: 14px;
  font-family: inherit;
  transition: all 0.2s ease;
}

.text-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.text-input::placeholder {
  color: #6c757d;
  font-style: italic;
}

.action-cell {
  text-align: center;
  width: 80px;
}

.remove-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-btn svg {
  width: 16px;
  height: 16px;
}

.remove-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* Button Styles */
.cancel-btn, .save-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #5a6268;
}

.save-btn {
  background: #28a745;
  color: white;
}

.save-btn:hover:not(:disabled) {
  background: #218838;
}

.save-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

/* Responsive design */
@media (max-width: 768px) {
  .page-main {
    padding: 16px;
  }
  
  .table-container {
    max-height: calc(100vh - 160px);
  }
  
  .table-header {
    padding: 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .table-header-right {
    width: 100%;
    justify-content: flex-start;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .edit-btn, .delete-btn {
    padding: 6px;
    width: 28px;
    height: 28px;
  }

  .edit-btn svg, .delete-btn svg {
    width: 14px;
    height: 14px;
  }

  .bulk-delete-btn {
    font-size: 12px;
    padding: 6px 12px;
    margin-right: 8px;
  }

  .language-selection-section {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .add-pair-btn {
    width: 100%;
  }

  .modal-content {
    width: 95%;
    margin: 20px;
  }

  .modal-header, .modal-body, .modal-footer {
    padding: 16px;
  }

  .modal-footer {
    flex-direction: column;
    gap: 8px;
  }

  .cancel-btn, .save-btn {
    width: 100%;
  }
  
  .glossary-table {
    font-size: 12px;
  }
  
  .glossary-table th,
  .glossary-table td {
    padding: 12px 8px;
  }
  
  .text-cell {
    max-width: 150px;
  }
  
  .text-content {
    max-height: 60px;
  }
  .modal-body .form-group {
  margin-bottom: 16px;
}

.modal-body .form-group label {
  display: block;
  font-weight: 600;
  margin-bottom: 6px;
}

.modal-body .form-control {
  width: 100%;
  padding: 8px 10px;
  border-radius: 6px;
  border: 1px solid #ccc;
  font-size: 14px;
  resize: vertical;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}

}
