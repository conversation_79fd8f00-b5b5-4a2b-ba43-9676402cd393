import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import './GlossaryTable.css';
import Header from '../Header';
import { getGlossary, createGlossary, deleteGlossary, updateGlossary } from '../../ServiceApi/Api';

const GlossaryTablePage = () => {
  const [loading, setLoading] = useState(false);
  const [glossaryData, setGlossaryData] = useState([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [languagePairs, setLanguagePairs] = useState([]);
  const [sourceLanguage, setSourceLanguage] = useState('');
  const [targetLanguage, setTargetLanguage] = useState('');

  const navigate = useNavigate();

  const languages = [
    { code: 'en', name: 'English' },
    { code: 'tr', name: 'Turkish' },
    { code: 'pt-br', name: 'Portuguese (Brazil)' },
    { code: 'id', name: 'Indonesian' }
  ];

  const handleBackToDashboard = () => navigate('/dashboard');

  const handleAddLanguagePair = () => {
    if (sourceLanguage && targetLanguage && sourceLanguage !== targetLanguage) {
      const newPair = {
        id: Date.now(),
        sourceLanguage,
        targetLanguage,
        sourceText: '',
        targetText: ''
      };
      setLanguagePairs([...languagePairs, newPair]);
      setSourceLanguage('');
      setTargetLanguage('');
    }
  };

  const handleTextChange = (id, field, value) => {
    setLanguagePairs(prev =>
      prev.map(pair =>
        pair.id === id ? { ...pair, [field]: value } : pair
      )
    );
  };

  const handleRemovePair = (id) => {
    setLanguagePairs(prev => prev.filter(pair => pair.id !== id));
  };

  const handleSubmit = async () => {
    try {
      const transformedPairs = languagePairs.map(pair => ({
        source_lang: languages.find(l => l.code === pair.sourceLanguage)?.name + ' - ' + pair.sourceLanguage,
        target_lang: languages.find(l => l.code === pair.targetLanguage)?.name + ' - ' + pair.targetLanguage,
        source: pair.sourceText,
        target: pair.targetText,
      }));
      await createGlossary({ entries: transformedPairs });
      const response = await getGlossary();
      setGlossaryData(response?.data || []);
      setShowCreateModal(false);
      setLanguagePairs([]);
      setSourceLanguage('');
      setTargetLanguage('');
    } catch (error) {
      console.error('Failed to submit glossary:', error);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this glossary item?')) {
      try {
        await deleteGlossary(id);
        const response = await getGlossary();
        setGlossaryData(response?.data || []);
      } catch (error) {
        console.error('Failed to delete glossary item:', error);
      }
    }
  };

  const handleEdit = (item) => {
    setEditingItem({
      ...item,
      source: item.source || '',
      target: item.target || ''
    });
    setShowEditModal(true);
  };

  const handleUpdate = async (e) => {
    e.preventDefault();
    if (!editingItem) return;
    try {
      await updateGlossary(editingItem.id, {
        source_lang: editingItem.source_lang,
        target_lang: editingItem.target_lang,
        source: editingItem.source,
        target: editingItem.target
      });
      setShowEditModal(false);
      setEditingItem(null);
      const response = await getGlossary();
      setGlossaryData(response?.data || []);
    } catch (error) {
      console.error('Failed to update glossary item:', error);
    }
  };

  const handleEditCancel = () => {
    setShowEditModal(false);
    setEditingItem(null);
  };

  const handleCreateCancel = () => {
    setShowCreateModal(false);
    setLanguagePairs([]);
    setSourceLanguage('');
    setTargetLanguage('');
  };

  useEffect(() => {
    const fetchGlossary = async () => {
      setLoading(true);
      try {
        const response = await getGlossary();
        setGlossaryData(response?.data || []);
      } catch (error) {
        console.error('Failed to fetch glossary:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchGlossary();
  }, []);

  return (
    <div className="glossary-table-page">
      <Header />
      <main className="page-main">
        <div className="table-container">
          {loading ? (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>Loading glossary...</p>
            </div>
          ) : (
            <div className="table-wrapper">
              <div className="table-header">
                <div className="table-header-left">
                  <h2>Glossary Management</h2>
                  <div className="table-count-box">
                    <span>📚</span>
                    <span>Total Items: {glossaryData.length}</span>
                  </div>
                </div>
                <div className="table-header-right">
                  <button className="create-glossary-btn" onClick={() => setShowCreateModal(true)}>
                    ➕ Create New Glossary
                  </button>
                </div>
              </div>
              {glossaryData.length > 0 ? (
                <div className="table-scroll">
                  <table className="glossary-table">
                    <thead>
                      <tr>
                        <th>ID</th>
                        <th>Source Language</th>
                        <th>Target Language</th>
                        <th>Source Text</th>
                        <th>Target Text</th>
                        <th>Created By</th>
                        <th>Updated By</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {glossaryData.map((item, index) => (
                        <tr key={item.id || index}>
                          <td>{index + 1}</td>
                          <td>{item.source_lang}</td>
                          <td>{item.target_lang}</td>
                          <td>{item.source}</td>
                          <td>{item.target}</td>
                          <td>{item.created_by?.name || '--'}</td>
                          <td>{item.updated_by?.name || '--'}</td>
                          <td>
                            <button className="btn btn-primary btn-sm me-2" onClick={() => handleEdit(item)}>Edit</button>
                            <button className="btn btn-danger btn-sm" onClick={() => handleDelete(item.id)}>Delete</button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="no-data-container">
                  <h2>No Glossary Items Found</h2>
                  <p>Click "Create New Glossary" to add language pairs and start building your glossary.</p>
                  <button className="back-button" onClick={handleBackToDashboard}>Back to Dashboard</button>
                </div>
              )}
            </div>
          )}
        </div>
      </main>

      {/* Create Glossary Modal */}
      {showCreateModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Create New Glossary</h3>
              <button className="modal-close" onClick={handleCreateCancel}>✖</button>
            </div>
            <div className="modal-body">
              <div className="language-selection-section">
                <div className="form-group">
                  <label>Source Language</label>
                  <select className="form-select" value={sourceLanguage} onChange={(e) => setSourceLanguage(e.target.value)}>
                    <option value="">Select Source</option>
                    {languages.map((lang) => (
                      <option key={lang.code} value={lang.code}>{lang.name}</option>
                    ))}
                  </select>
                </div>
                <div className="form-group">
                  <label>Target Language</label>
                  <select className="form-select" value={targetLanguage} onChange={(e) => setTargetLanguage(e.target.value)}>
                    <option value="">Select Target</option>
                    {languages.map((lang) => (
                      <option key={lang.code} value={lang.code}>{lang.name}</option>
                    ))}
                  </select>
                </div>
                <button className="add-pair-btn" onClick={handleAddLanguagePair} disabled={!sourceLanguage || !targetLanguage || sourceLanguage === targetLanguage}>
                  ➕ Add Pair
                </button>
              </div>
              {languagePairs.length > 0 && (
                <div className="language-pairs-section">
                  <div className="section-title">Language Pairs <span className="pairs-count">({languagePairs.length})</span></div>
                  <div className="pairs-table-container">
                    <table className="pairs-table">
                      <thead>
                        <tr>
                          <th>Source Language</th>
                          <th>Target Language</th>
                          <th>Source Text</th>
                          <th>Target Text</th>
                          <th className="action-cell">Remove</th>
                        </tr>
                      </thead>
                      <tbody>
                        {languagePairs.map((pair) => (
                          <tr key={pair.id}>
                            <td>{languages.find(l => l.code === pair.sourceLanguage)?.name}</td>
                            <td>{languages.find(l => l.code === pair.targetLanguage)?.name}</td>
                            <td><textarea className="text-input" value={pair.sourceText} onChange={(e) => handleTextChange(pair.id, 'sourceText', e.target.value)} /></td>
                            <td><textarea className="text-input" value={pair.targetText} onChange={(e) => handleTextChange(pair.id, 'targetText', e.target.value)} /></td>
                            <td><button className="remove-btn" onClick={() => handleRemovePair(pair.id)}>✖</button></td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
            <div className="modal-footer">
              <button className="cancel-btn" onClick={handleCreateCancel}>Cancel</button>
              <button className="save-btn" onClick={handleSubmit} disabled={languagePairs.length === 0}>Save Glossary</button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      {showEditModal && editingItem && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Edit Glossary Item</h3>
              <button className="modal-close" onClick={handleEditCancel}>✖</button>
            </div>
            <form onSubmit={handleUpdate}>
              <div className="modal-body">
                <div className="form-group">
                  <label>Source Language</label>
                  <input type="text" className="form-control" value={editingItem.source_lang} onChange={(e) => setEditingItem({ ...editingItem, source_lang: e.target.value })} required />
                </div>
                <div className="form-group">
                  <label>Target Language</label>
                  <input type="text" className="form-control" value={editingItem.target_lang} onChange={(e) => setEditingItem({ ...editingItem, target_lang: e.target.value })} required />
                </div>
                <div className="form-group">
                  <label>Source Text</label>
                  <textarea className="form-control" value={editingItem.source} onChange={(e) => setEditingItem({ ...editingItem, source: e.target.value })} required />
                </div>
                <div className="form-group">
                  <label>Target Text</label>
                  <textarea className="form-control" value={editingItem.target} onChange={(e) => setEditingItem({ ...editingItem, target: e.target.value })} required />
                </div>
              </div>
              <div className="modal-footer">
                <button type="button" className="cancel-btn" onClick={handleEditCancel}>Cancel</button>
                <button type="submit" className="save-btn">Update</button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default GlossaryTablePage;