import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import './GlossaryTable.css';
import Header from '../Header';
import { getGlossary, createGlossary, deleteGlossary, updateGlossary } from '../../ServiceApi/Api';

const GlossaryTablePage = () => {
  const [loading, setLoading] = useState(false);
  const [glossaryData, setGlossaryData] = useState([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [languagePairs, setLanguagePairs] = useState([]);
  const [sourceLanguage, setSourceLanguage] = useState('');
  const [targetLanguage, setTargetLanguage] = useState('');

  const navigate = useNavigate();

  const languages = [
    { code: 'en', name: 'English' },
    { code: 'tr', name: 'Turkish' },
    { code: 'pt-br', name: 'Portuguese (Brazil)' },
    { code: 'id', name: 'Indonesian' }
  ];

  const handleBackToDashboard = () => navigate('/dashboard');

  const handleAddLanguagePair = () => {
    if (sourceLanguage && targetLanguage && sourceLanguage !== targetLanguage) {
      const newPair = {
        id: Date.now(),
        sourceLanguage,
        targetLanguage,
        sourceText: '',
        targetText: ''
      };
      setLanguagePairs([...languagePairs, newPair]);
      setSourceLanguage('');
      setTargetLanguage('');
    }
  };

  const handleTextChange = (id, field, value) => {
    setLanguagePairs(prev =>
      prev.map(pair =>
        pair.id === id ? { ...pair, [field]: value } : pair
      )
    );
  };

  const handleRemovePair = (id) => {
    setLanguagePairs(prev => prev.filter(pair => pair.id !== id));
  };

  const handleSubmit = async () => {
    try {
      const transformedPairs = languagePairs.map(pair => ({
        source_lang: languages.find(l => l.code === pair.sourceLanguage)?.name + ' - ' + pair.sourceLanguage,
        target_lang: languages.find(l => l.code === pair.targetLanguage)?.name + ' - ' + pair.targetLanguage,
        source: pair.sourceText,
        target: pair.targetText,
      }));
      await createGlossary({ entries: transformedPairs });
      const response = await getGlossary();
      setGlossaryData(response?.data || []);
      setShowCreateModal(false);
      setLanguagePairs([]);
      setSourceLanguage('');
      setTargetLanguage('');
    } catch (error) {
      console.error('Failed to submit glossary:', error);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this glossary item?')) {
      try {
        await deleteGlossary(id);
        const response = await getGlossary();
        setGlossaryData(response?.data || []);
      } catch (error) {
        console.error('Failed to delete glossary item:', error);
      }
    }
  };

  const handleEdit = (item) => {
    setEditingItem({
      ...item,
      source: item.source || '',
      target: item.target || ''
    });
    setShowEditModal(true);
  };

  const handleUpdate = async (e) => {
    e.preventDefault();
    if (!editingItem) return;
    try {
      await updateGlossary(editingItem.id, {
        source_lang: editingItem.source_lang,
        target_lang: editingItem.target_lang,
        source: editingItem.source,
        target: editingItem.target
      });
      setShowEditModal(false);
      setEditingItem(null);
      const response = await getGlossary();
      setGlossaryData(response?.data || []);
    } catch (error) {
      console.error('Failed to update glossary item:', error);
    }
  };

  const handleEditCancel = () => {
    setShowEditModal(false);
    setEditingItem(null);
  };

  const handleCreateCancel = () => {
    setShowCreateModal(false);
    setLanguagePairs([]);
    setSourceLanguage('');
    setTargetLanguage('');
  };

  useEffect(() => {
    const fetchGlossary = async () => {
      setLoading(true);
      try {
        const response = await getGlossary();
        setGlossaryData(response?.data || []);
      } catch (error) {
        console.error('Failed to fetch glossary:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchGlossary();
  }, []);

  return (
    <div className="glossary-table-page">
      <Header />
      <main className="page-main">
        <div className="table-container">
          {loading ? (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>Loading glossary...</p>
            </div>
          ) : (
            <div className="table-wrapper">
              <div className="table-header">
                <div className="table-header-left">
                  <h2>Glossary Management</h2>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      backgroundColor: '#e8f0fe',
                      padding: '6px 12px',
                      borderRadius: '10px',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#1a3e9a',
                      width: 'fit-content',
                      boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
                      marginBottom: '10px',
                    }}
                  >
                    <svg
                      width="18"
                      height="18"
                      fill="#1a3e9a"
                      viewBox="0 0 24 24"
                    >
                      <path d="M12 2C13.1 2 14 2.9 14 4V5H20C20.6 5 21 5.4 21 6S20.6 7 20 7H19V19C19 20.1 18.1 21 17 21H7C5.9 21 5 20.1 5 19V7H4C3.4 7 3 6.6 3 6S3.4 5 4 5H10V4C10 2.9 10.9 2 12 2M7 7V19H17V7H7M9 8H11V18H9V8M13 8H15V18H13V8Z" />
                    </svg>
                    <span>Total: {glossaryData.length} glossary items</span>
                  </div>
                </div>
                <div className="table-header-right">
                  <button className="create-glossary-btn" onClick={() => setShowCreateModal(true)}>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="12" y1="5" x2="12" y2="19" />
                      <line x1="5" y1="12" x2="19" y2="12" />
                    </svg>
                    Create New Glossary
                  </button>
                </div>
              </div>
              {glossaryData.length > 0 ? (
                <div className="table-scroll">
                  <table className="glossary-table">
                    <thead>
                      <tr>
                        <th>ID</th>
                        <th>Source Language</th>
                        <th>Target Language</th>
                        <th>Source Text</th>
                        <th>Target Text</th>
                        <th>Created By</th>
                        <th>Updated By</th>
                        <th className="action-header">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {glossaryData.map((item, index) => (
                        <tr key={item.id || index}>
                          <td>{index + 1}</td>
                          <td>{item.source_lang}</td>
                          <td>{item.target_lang}</td>
                          <td>{item.source}</td>
                          <td>{item.target}</td>
                          <td>{item.created_by?.name || '--'}</td>
                          <td>{item.updated_by?.name || '--'}</td>
                          <td className="action-cell">
                            <div className="action-buttons">
                              <button
                                className="edit-btn"
                                onClick={() => handleEdit(item)}
                                title="Edit Glossary Item"
                              >
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                                  <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                                </svg>
                              </button>
                              <button
                                className="delete-btn"
                                onClick={() => handleDelete(item.id)}
                                title="Delete Glossary Item"
                              >
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <polyline points="3,6 5,6 21,6" />
                                  <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2" />
                                  <line x1="10" y1="11" x2="10" y2="17" />
                                  <line x1="14" y1="11" x2="14" y2="17" />
                                </svg>
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="no-data-container">
                  <h2>No Glossary Items Found</h2>
                  <p>Click "Create New Glossary" to add language pairs and start building your glossary.</p>
                  <button className="back-button" onClick={handleBackToDashboard}>Back to Dashboard</button>
                </div>
              )}
            </div>
          )}
        </div>
      </main>

      {/* Create Glossary Modal */}
      {showCreateModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Create New Glossary</h3>
              <button className="modal-close" onClick={handleCreateCancel}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18" />
                  <line x1="6" y1="6" x2="18" y2="18" />
                </svg>
              </button>
            </div>
            <div className="modal-body">
              <div className="language-selection-section">
                <div className="form-group">
                  <label>Source Language</label>
                  <select className="form-select" value={sourceLanguage} onChange={(e) => setSourceLanguage(e.target.value)}>
                    <option value="">Select Source</option>
                    {languages.map((lang) => (
                      <option key={lang.code} value={lang.code}>{lang.name}</option>
                    ))}
                  </select>
                </div>
                <div className="form-group">
                  <label>Target Language</label>
                  <select className="form-select" value={targetLanguage} onChange={(e) => setTargetLanguage(e.target.value)}>
                    <option value="">Select Target</option>
                    {languages.map((lang) => (
                      <option key={lang.code} value={lang.code}>{lang.name}</option>
                    ))}
                  </select>
                </div>
                <button className="add-pair-btn" onClick={handleAddLanguagePair} disabled={!sourceLanguage || !targetLanguage || sourceLanguage === targetLanguage}>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="12" y1="5" x2="12" y2="19" />
                    <line x1="5" y1="12" x2="19" y2="12" />
                  </svg>
                  Add Pair
                </button>
              </div>
              {languagePairs.length > 0 && (
                <div className="language-pairs-section">
                  <div className="section-title">Language Pairs <span className="pairs-count">({languagePairs.length})</span></div>
                  <div className="pairs-table-container">
                    <table className="pairs-table">
                      <thead>
                        <tr>
                          <th>Source Language</th>
                          <th>Target Language</th>
                          <th>Source Text</th>
                          <th>Target Text</th>
                          <th className="action-cell">Remove</th>
                        </tr>
                      </thead>
                      <tbody>
                        {languagePairs.map((pair) => (
                          <tr key={pair.id}>
                            <td>{languages.find(l => l.code === pair.sourceLanguage)?.name}</td>
                            <td>{languages.find(l => l.code === pair.targetLanguage)?.name}</td>
                            <td><textarea className="text-input" value={pair.sourceText} onChange={(e) => handleTextChange(pair.id, 'sourceText', e.target.value)} /></td>
                            <td><textarea className="text-input" value={pair.targetText} onChange={(e) => handleTextChange(pair.id, 'targetText', e.target.value)} /></td>
                            <td>
                              <button className="remove-btn" onClick={() => handleRemovePair(pair.id)}>
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <line x1="18" y1="6" x2="6" y2="18" />
                                  <line x1="6" y1="6" x2="18" y2="18" />
                                </svg>
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
            <div className="modal-footer">
              <button className="cancel-btn" onClick={handleCreateCancel}>Cancel</button>
              <button className="save-btn" onClick={handleSubmit} disabled={languagePairs.length === 0}>Save Glossary</button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      {showEditModal && editingItem && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Edit Glossary Item</h3>
              <button className="modal-close" onClick={handleEditCancel}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18" />
                  <line x1="6" y1="6" x2="18" y2="18" />
                </svg>
              </button>
            </div>
            <form onSubmit={handleUpdate}>
              <div className="modal-body">
                <div className="form-group">
                  <label>Source Language</label>
                  <input type="text" className="form-control" value={editingItem.source_lang} onChange={(e) => setEditingItem({ ...editingItem, source_lang: e.target.value })} required />
                </div>
                <div className="form-group">
                  <label>Target Language</label>
                  <input type="text" className="form-control" value={editingItem.target_lang} onChange={(e) => setEditingItem({ ...editingItem, target_lang: e.target.value })} required />
                </div>
                <div className="form-group">
                  <label>Source Text</label>
                  <textarea className="form-control" value={editingItem.source} onChange={(e) => setEditingItem({ ...editingItem, source: e.target.value })} required />
                </div>
                <div className="form-group">
                  <label>Target Text</label>
                  <textarea className="form-control" value={editingItem.target} onChange={(e) => setEditingItem({ ...editingItem, target: e.target.value })} required />
                </div>
              </div>
              <div className="modal-footer">
                <button type="button" className="cancel-btn" onClick={handleEditCancel}>Cancel</button>
                <button type="submit" className="save-btn">Update</button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default GlossaryTablePage;