import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Swal from 'sweetalert2';
import { getAllUsers, addUser, updateUser, deleteUsers } from '../../ServiceApi/Api';
import './UserTablePage.css';
import Header from '../Header';

const UserTablePage = () => {
    const [users, setUsers] = useState([]);
    const [filteredUsers, setFilteredUsers] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedItems, setSelectedItems] = useState([]);
    const [selectAll, setSelectAll] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [editingUser, setEditingUser] = useState(null);
    const [createForm, setCreateForm] = useState({ name: '', email: '', password: '', confirmPassword: '' });
    const [editForm, setEditForm] = useState({ id: '', name: '', email: '' });
    // State for dynamic email input styling
    const [createEmailFocused, setCreateEmailFocused] = useState(false);
    const [editEmailFocused, setEditEmailFocused] = useState(false);
    const [search, setSearch] = useState('');
    const navigate = useNavigate();

    // Fetch all users on mount
    useEffect(() => {
        fetchUsers();
    }, []);

    const fetchUsers = async () => {
        try {
            setLoading(true);
            const response = await getAllUsers();
            let usersData = [];
            if (response && response.success && Array.isArray(response.users)) {
                usersData = response.users;
            } else if (response && (response.message === 'Invalid or expired token' || response.message === 'Forbidden')) {
                // Token expired or forbidden, log out and redirect
                Swal.fire({
                    icon: 'error',
                    title: 'Session expired',
                    text: 'Please log in again.',
                    timer: 2000,
                    showConfirmButton: false
                }).then(() => {
                    logoutUser();
                    navigate('/login');
                });
                return;
            }
            setUsers(usersData);
            setFilteredUsers(usersData);
        } catch (error) {
            setUsers([]);
            setFilteredUsers([]);
            // Optionally handle error here
        } finally {
            setLoading(false);
        }
    };

    // Filter users by name only
    useEffect(() => {
        if (!search.trim()) {
            setFilteredUsers(users);
        } else {
            setFilteredUsers(users.filter(u =>
                u.name && u.name.toLowerCase().includes(search.toLowerCase())
            ));
        }
    }, [search, users]);

    const handleSelectAll = (checked) => {
        setSelectAll(checked);
        if (checked) {
            setSelectedItems(filteredUsers.map(u => u.id));
        } else {
            setSelectedItems([]);
        }
    };

    const handleSelectItem = (id, checked) => {
        if (checked) {
            setSelectedItems(prev => [...prev, id]);
        } else {
            setSelectedItems(prev => prev.filter(item => item !== id));
            setSelectAll(false);
        }
    };

    const handleEdit = (user) => {
        setEditingUser(user);
        setEditForm({
            id: user.id,
            name: user.name,
            email: user.email,
        });
        setShowEditModal(true);
    };

    const handleDelete = async (user) => {
        if (window.confirm(`Are you sure you want to delete user ID: ${user.id}?`)) {
            try {
                setLoading(true);
                await deleteUsers([user.id]);
                Swal.fire({
                    title: 'Success!',
                    text: 'User deleted successfully',
                    icon: 'success',
                    timer: 3000,
                    showConfirmButton: false
                });
                fetchUsers();
            } catch (error) {
                Swal.fire({
                    title: 'Error!',
                    text: error.message || 'Failed to delete user',
                    icon: 'error',
                    timer: 3000,
                    showConfirmButton: false
                });
            } finally {
                setLoading(false);
            }
        }
    };

    const handleBulkDelete = async () => {
        if (selectedItems.length === 0) return;
        if (window.confirm(`Are you sure you want to delete ${selectedItems.length} selected users?`)) {
            try {
                setLoading(true);
                await deleteUsers(selectedItems);
                Swal.fire({
                    title: 'Success!',
                    text: `${selectedItems.length} users deleted successfully`,
                    icon: 'success',
                    timer: 3000,
                    showConfirmButton: false
                });
                setSelectedItems([]);
                setSelectAll(false);
                fetchUsers();
            } catch (error) {
                Swal.fire({
                    title: 'Error!',
                    text: error.message || 'Failed to delete users',
                    icon: 'error',
                    timer: 3000,
                    showConfirmButton: false
                });
            } finally {
                setLoading(false);
            }
        }
    };

    // Validation state for create form
    const [createErrors, setCreateErrors] = useState({});

    const handleCreateUser = async () => {
        const { name, email, password, confirmPassword } = createForm;
        const errors = {};
        if (!name) errors.name = 'Please fill the name field';
        if (!email) errors.email = 'Please fill the email field';
        if (!password) errors.password = 'Please fill the password field';
        if (!confirmPassword) errors.confirmPassword = 'Please fill the confirm password field';
        if (password && confirmPassword && password !== confirmPassword) {
            errors.confirmPassword = 'Password and Confirm Password must match!';
        }
        setCreateErrors(errors);
        if (Object.keys(errors).length > 0) return;
        try {
            setLoading(true);
            const response = await addUser({ name, email, password });
            if (response.success === true) {
                Swal.fire({
                    icon: 'success',
                    title: 'User created successfully!',
                    showConfirmButton: false,
                    timer: 1500
                });
                setShowCreateModal(false);
                setCreateForm({ name: '', email: '', password: '', confirmPassword: '' });
                setCreateErrors({});
                fetchUsers();
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: response.message || 'Failed to create user!'
                });
            }
        } catch (error) {
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: error.message || 'Failed to create user!'
            });
        } finally {
            setLoading(false);
        }
    };

    const handleEditSubmit = async () => {
        try {
            setLoading(true);

            if (!editForm.id) {
                throw new Error('User ID is required');

            }
            const { name, email, id } = editForm;
            if (!name || !email) {
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'Name and Email are required!',
                });
                return;
            }
            const response = await updateUser({ name, email }, id);
            if (response.success !== true) {
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'Failed to update user!',
                });
                return;
            }
            Swal.fire({
                icon: 'success',
                title: 'User updated successfully!',
                showConfirmButton: false,
                timer: 1500
            });
            setShowEditModal(false);
            setEditingUser(null);
            setEditForm({ id: '', name: '', email: '' });
            fetchUsers();

        } catch (error) {
            Swal.fire({
                title: 'Error!',
                text: error.message || 'Failed to update user',
                icon: 'error',
                timer: 3000,
                showConfirmButton: false
            });
        } finally {
            setLoading(false);
        }
    };

    const handleLogout = async () => {
        const result = await Swal.fire({
            title: 'Are you sure?',
            text: 'You will be logged out of your account',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, logout!'
        });
        if (result.isConfirmed) {
            logoutUser();
            await Swal.fire({
                title: 'Logged out!',
                text: 'You have been successfully logged out.',
                icon: 'success',
                timer: 3000,
                showConfirmButton: false
            });
            navigate('/login');
        }
    };

    const handleBackToDashboard = () => {
        navigate('/dashboard');
    };

    const handleCreateCancel = () => {
        setShowCreateModal(false);
    };

    const handleEditCancel = () => {
        setShowEditModal(false);
        setEditingUser(null);
        setEditForm({ id: '', name: '', email: '' });
    };

    const handleCreateFormChange = (field, value) => {
        setCreateForm(prev => ({
            ...prev,
            [field]: value
        }));
        setCreateErrors(prev => ({ ...prev, [field]: undefined }));
    };

    const handleEditFormChange = (field, value) => {
        setEditForm(prev => ({
            ...prev,
            [field]: value
        }));
    };

    if (loading) {
        return (
            <div className="loading-container">
                <div className="loading-spinner"></div>
                <p>Loading users...</p>
            </div>
        );
    }

    return (
        <div className="user-table-page">
            <Header />
            <main className="page-main">
                <div className="table-container">
                    <div className="table-header">
                        <div className="table-header-left">
                            <h2>Users List</h2>
                            <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      backgroundColor: '#e8f0fe',
                      padding: '6px 12px',
                      borderRadius: '10px',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#1a3e9a',
                      width: 'fit-content',
                      boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
                      marginBottom: '10px',
                    }}
                  >
                    <svg
                      width="18"
                      height="18"
                      fill="#1a3e9a"
                      viewBox="0 0 24 24"
                    >
                      <path d="M3 6h18v2H3V6zm0 5h18v2H3v-2zm0 5h12v2H3v-2z" />
                    </svg>
                    <span>Total: {users.length} users</span>
                  </div>
                        </div>
                        <div className="table-header-right">
                            <label htmlFor="title-search">Search:</label>
                            <input
                                id="title-search"
                                type="text"
                                placeholder="Enter text..."
                                value={search}
                                onChange={e => setSearch(e.target.value)}
                                style={{
                                    padding: '6px 10px',
                                    fontSize: '14px',
                                    borderRadius: '4px',
                                    border: '1px solid #ccc',
                                    width: '200px',
                                    marginRight: '10px'
                                }}
                            />
                            {selectedItems.length > 0 && (
                                <button
                                    className="bulk-delete-btn"
                                    onClick={handleBulkDelete}
                                    disabled={loading}
                                    title={`Delete ${selectedItems.length} selected items`}
                                >
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                        <polyline points="3,6 5,6 21,6" />
                                        <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2" />
                                        <line x1="10" y1="11" x2="10" y2="17" />
                                        <line x1="14" y1="11" x2="14" y2="17" />
                                    </svg>
                                    {loading ? 'Deleting...' : `Delete All (${selectedItems.length})`}
                                </button>
                            )}
                            <div style={{ padding: "0 10px" }}>
                                <button
                                    onClick={() => setShowCreateModal(true)}
                                    style={{
                                        padding: '8px 16px',
                                        fontSize: '14px',
                                        borderRadius: '4px',
                                        border: '1px solid #ccc',
                                        width: '200px',
                                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                        color: '#fff',
                                    }}
                                >
                                    Add New User
                                </button>
                            </div>

                        </div>
                    </div>
                    <div className="table-scroll">
                        <table className="brands-table">
                            <thead>
                                <tr>
                                    <th className="checkbox-header">
                                        <input
                                            type="checkbox"
                                            checked={selectAll}
                                            onChange={e => handleSelectAll(e.target.checked)}
                                            title="Select All"
                                        />
                                    </th>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Created At</th>
                                    <th>Last Login</th>
                                    <th className="action-header">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {filteredUsers.map((user, index) => (
                                    <tr key={user.id || index}>
                                        <td className="checkbox-cell">
                                            <input
                                                type="checkbox"
                                                checked={selectedItems.includes(user.id)}
                                                onChange={e => handleSelectItem(user.id, e.target.checked)}
                                                title="Select this item"
                                            />
                                        </td>
                                        <td>{user.id}</td>
                                        <td>{user.name}</td>
                                        <td>{user.email}</td>
                                        <td>{user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}</td>
                                        <td>{user.last_login ? new Date(user.last_login).toLocaleDateString() : 'N/A'}</td>
                                        <td className="action-cell">
                                            <div className="action-buttons">
                                                <button
                                                    className="edit-btn"
                                                    onClick={() => handleEdit(user)}
                                                    title="Edit User"
                                                >
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                                                        <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                                                    </svg>
                                                </button>
                                                <button
                                                    className="delete-btn"
                                                    onClick={() => handleDelete(user)}
                                                    title="Delete User"
                                                >
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                        <polyline points="3,6 5,6 21,6" />
                                                        <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2" />
                                                        <line x1="10" y1="11" x2="10" y2="17" />
                                                        <line x1="14" y1="11" x2="14" y2="17" />
                                                    </svg>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>

            {/* Create Modal */}
            {showCreateModal && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>Add User</h3>
                            <button className="modal-close" onClick={handleCreateCancel}>
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                    <line x1="18" y1="6" x2="6" y2="18" />
                                    <line x1="6" y1="6" x2="18" y2="18" />
                                </svg>
                            </button>
                        </div>
                        <div className="modal-body">
                            <div className="form-group">
                                <label htmlFor="user-name">Name:<span style={{ color: "red" }}>*</span></label>
                                <input
                                    type="text"
                                    id="user-name"
                                    value={createForm.name}
                                    onChange={(e) => handleCreateFormChange('name', e.target.value)}
                                    placeholder="Enter User name"
                                    style={{
                                        width: '100%',
                                        padding: '8px',
                                        fontSize: '14px',
                                        borderRadius: '4px',
                                        border: createErrors.name ? '2px solid red' : '1px solid #ccc',
                                        marginBottom: '4px',
                                    }}
                                    required
                                />
                                {createErrors.name && <div style={{ color: 'red', fontSize: '13px', marginBottom: '8px' }}>{createErrors.name}</div>}
                            </div>
                            <div className="form-group">
                                <label htmlFor="user-email">Email:<span style={{ color: "red" }}>*</span></label>
                                <input
                                    type="email"
                                    id="user-email"
                                    value={createForm.email}
                                    className='form-input'
                                    onChange={(e) => handleCreateFormChange('email', e.target.value)}
                                    placeholder="Enter User email"
                                    style={{
                                        width: '100%',
                                        padding: '8px',
                                        fontSize: '14px',
                                        borderRadius: '4px',
                                        border: createErrors.email ? '2px solid red' : (createEmailFocused ? '2px solid #3b82f6' : '1px solid #ccc'),
                                        marginBottom: '4px',
                                        outline: createEmailFocused ? 'none' : undefined,
                                        background: createEmailFocused ? '#f0f7ff' : undefined,
                                        transition: 'border 0.2s, background 0.2s',
                                        backgroundImage: 'none',
                                        boxShadow: 'none',
                                    }}
                                    required
                                />
                                {createErrors.email && <div style={{ color: 'red', fontSize: '13px', marginBottom: '8px' }}>{createErrors.email}</div>}
                            </div>
                            <div className="form-group">
                                <label htmlFor="user-password">Password:<span style={{ color: "red" }}>*</span></label>
                                <input
                                    type="password"
                                    id="user-password"
                                    value={createForm.password}
                                    onChange={(e) => handleCreateFormChange('password', e.target.value)}
                                    tooltip="Admin@123"
                                    placeholder="Enter password like Admin@123"
                                    style={{
                                        width: '100%',
                                        padding: '8px',
                                        fontSize: '14px',
                                        borderRadius: '4px',
                                        border: createErrors.password ? '2px solid red' : '1px solid #ccc',
                                        marginBottom: '4px',
                                    }}
                                    required
                                />
                                {createErrors.password && <div style={{ color: 'red', fontSize: '13px', marginBottom: '8px' }}>{createErrors.password}</div>}
                            </div>
                            <div className="form-group">
                                <label htmlFor="user-confirm-password">Confirm Password:<span style={{ color: "red" }}>*</span></label>
                                <input
                                    type="password"
                                    id="user-confirm-password"
                                    value={createForm.confirmPassword}
                                    onChange={(e) => handleCreateFormChange('confirmPassword', e.target.value)}
                                    placeholder="Confirm password"
                                    style={{
                                        width: '100%',
                                        padding: '8px',
                                        fontSize: '14px',
                                        borderRadius: '4px',
                                        border: createErrors.confirmPassword ? '2px solid red' : '1px solid #ccc',
                                        marginBottom: '4px',
                                    }}
                                    required
                                />
                                {createErrors.confirmPassword && <div style={{ color: 'red', fontSize: '13px', marginBottom: '8px' }}>{createErrors.confirmPassword}</div>}
                            </div>

                        </div>
                        <div className="modal-footer">
                            <button className="cancel-btn" onClick={handleCreateCancel}>
                                Cancel
                            </button>
                            <button className="save-btn" onClick={handleCreateUser} disabled={loading}>
                                {loading ? 'Saving...' : 'Save Changes'}
                            </button>
                        </div>
                    </div>
                </div>
            )}
            {/* Edit Modal */}
            {showEditModal && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>Edit User</h3>
                            <button className="modal-close" onClick={handleEditCancel}>
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                    <line x1="18" y1="6" x2="6" y2="18" />
                                    <line x1="6" y1="6" x2="18" y2="18" />
                                </svg>
                            </button>
                        </div>
                        <div className="modal-body">
                            <div className="form-group">
                                <label htmlFor="user-name">Name:<span style={{ color: "red" }}>*</span></label>
                                <input
                                    type="text"
                                    id="user-name"
                                    value={editForm.name}
                                    onChange={(e) => handleEditFormChange('name', e.target.value)}
                                    placeholder="Enter user name"
                                    style={{
                                        width: '100%',
                                        padding: '8px',
                                        fontSize: '14px',
                                        borderRadius: '4px',
                                        border: '1px solid #ccc',
                                        marginBottom: '12px',
                                    }}
                                    required
                                />
                            </div>
                            <div className="form-group">
                                <label htmlFor="user-email">Email:<span style={{ color: "red" }}>*</span></label>
                                <input
                                    id="user-email"
                                    type="email"
                                    name="email"
                                    placeholder="Enter email..."
                                    value={editForm.email}
                                    onChange={e => handleEditFormChange('email', e.target.value)}
                                    style={{
                                        width: '100%',
                                        padding: '8px',
                                        fontSize: '14px',
                                        borderRadius: '4px',
                                        border: '1px solid #ccc',
                                        marginBottom: '12px',
                                        outline: editEmailFocused ? 'none' : undefined,
                                        background: editEmailFocused ? '#f0f7ff' : undefined,
                                        transition: 'border 0.2s, background 0.2s',
                                        backgroundImage: 'none',
                                        boxShadow: 'none',
                                    }}
                                    autoComplete="off"
                                    required
                                    inputMode="email"
                                    className='form-input'
                                />
                            </div>


                        </div>
                        <div className="modal-footer">
                            <button className="cancel-btn" onClick={handleEditCancel}>
                                Cancel
                            </button>
                            <button className="save-btn" onClick={handleEditSubmit} disabled={loading}>
                                {loading ? 'Saving...' : 'Save Changes'}
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default UserTablePage;